networks:
  nifi_network:
    driver: bridge
    ipam:
      config:
        - subnet: ***********/24

services:
  zookeeper1:
    image: 'docker.io/bitnami/zookeeper:latest'
    hostname: zookeeper1
    container_name: zookeeper1
    # mem_limit: 1g
    # cpus: 0.5
    environment:
      ALLOW_ANONYMOUS_LOGIN: "yes"
      ZOO_MY_ID: 1
      ZOO_SERVER_ID: 1
      ZOO_SERVERS: "zookeeper1:2888:3888,zookeeper2:2888:3888,zookeeper3:2888:3888"
      # ZOO_SERVERS: "server.1=zookeeper1:2888:3888;2181 server.2=zookeeper2:2888:3888;2181 server.3=zookeeper3:2888:3888;2181"
    networks:
      nifi_network:
        ipv4_address: ************
    volumes:
      # - zk_data1:/data
      - zk_conf1:/conf
      - zk_data1:/bitnami

  zookeeper2:
    image: 'docker.io/bitnami/zookeeper:latest'
    hostname: zookeeper2
    container_name: zookeeper2
    # mem_limit: 1g
    # cpus: 0.5
    environment:
      ALLOW_ANONYMOUS_LOGIN: "yes"
      ZOO_MY_ID: 2
      ZOO_SERVER_ID: 2
      ZOO_SERVERS: "zookeeper1:2888:3888,zookeeper2:2888:3888,zookeeper3:2888:3888"
      # ZOO_SERVERS: "server.1=zookeeper1:2888:3888;2181 server.2=zookeeper2:2888:3888;2181 server.3=zookeeper3:2888:3888;2181"
    networks:
      nifi_network:
        ipv4_address: ************
    volumes:
      # - zk_data2:/data
      - zk_conf2:/conf
      - zk_data2:/bitnami

  zookeeper3:
    image: 'docker.io/bitnami/zookeeper:latest'
    hostname: zookeeper3
    container_name: zookeeper3
    # mem_limit: 1g
    # cpus: 0.5
    environment:
      ALLOW_ANONYMOUS_LOGIN: "yes"
      ZOO_MY_ID: 3
      ZOO_SERVER_ID: 3
      ZOO_SERVERS: "zookeeper1:2888:3888,zookeeper2:2888:3888,zookeeper3:2888:3888"
      # ZOO_SERVERS: "server.1=zookeeper1:2888:3888;2181 server.2=zookeeper2:2888:3888;2181 server.3=zookeeper3:2888:3888;2181"
    networks:
      nifi_network:
        ipv4_address: ************
    volumes:
      # - zk_data3:/data
      - zk_conf3:/conf
      - zk_data3:/bitnami

  zoonavigator:
    image: 'docker.io/elkozmon/zoonavigator:latest'
    container_name: zoonavigator
    ports:
      - "19000:9000"
    networks:
      nifi_network:
        ipv4_address: ************
    depends_on:
      - zookeeper1
      - zookeeper2
      - zookeeper3

  nifi-registry:
    image: apache/nifi-registry:2.4.0
    hostname: nifi-registry
    container_name: nifi-registry
    mem_limit: 1g
    cpus: 0.5
    ports:
      - "${NIFI_REGISTRY_WEB_HTTP_PORT}:18080"
    networks:
      nifi_network:
        ipv4_address: ************

  nifi1:
    image: apache/nifi:2.4.0
    hostname: nifi1
    container_name: nifi1
    #mem_limit: 2g
    #cpus: 1.5
    user: nifi
    environment:
      NIFI_WEB_HTTPS_PORT: "8443"
      NIFI_CLUSTER_IS_NODE: "true"
      NIFI_ZK_CONNECT_STRING: "zookeeper1:2181,zookeeper2:2181,zookeeper3:2181"
      NIFI_ELECTION_MAX_WAIT: "1 min"
      NIFI_ELECTION_MAX_CANDIDATES: 1
      NIFI_SENSITIVE_PROPS_KEY: "MySecureKey123!"
      NIFI_CLUSTER_NODE_PROTOCOL_PORT: "8080"
      NIFI_WEB_PROXY_HOST: "nifi1:18443,nifi1:8443,nifi1,nifi2:18444,nifi2:8443,nifi2,nifi3:18445,nifi3:8443,nifi3,nifi:18443,nifi:18444,nifi:18445,localhost:18443,localhost:18444,localhost:18445,localhost:8080"
      NIFI_STATE_MANAGEMENT_EMBEDDED_ZOOKEEPER_START: "false"

      # Communication Security
      # NIFI_REMOTE_INPUT_SECURE: "true"

      # Authentication Configuration
      SINGLE_USER_CREDENTIALS_USERNAME: "nifi"
      SINGLE_USER_CREDENTIALS_PASSWORD: "nifipassword"
      NIFI_SECURITY_USER_AUTHORIZER: "single-user-authorizer"
      NIFI_SECURITY_USER_LOGIN_IDENTITY_PROVIDER: "single-user-provider"
      INITIAL_ADMIN_IDENTITY: admin
      # TLS Configuration
      AUTH: "tls"
      KEYSTORE_TYPE: "JKS"
      KEYSTORE_PASSWORD: "password123"
      KEY_PASSWORD: "password123"
      TRUSTSTORE_TYPE: "JKS"
      TRUSTSTORE_PASSWORD: "password123"
      # NIFI_CLUSTER_PROTOCOL_IS_SECURE: "true"
      # NIFI_WEB_HTTPS_NETWORK_INTERFACE_DEFAULT: ""

      NIFI_CLUSTER_ADDRESS: "nifi1"
      NIFI_CLUSTER_NODE_ADDRESS: "nifi1"
      NIFI_WEB_HTTPS_HOST: "nifi1"
      KEYSTORE_PATH: "/opt/certs/nifi1/keystore.jks"
      TRUSTSTORE_PATH: "/opt/certs/nifi1/truststore.jks"

      # Registry Configuration
      NIFI_REGISTRY_URL: "http://nifi-registry:18080"

    ports:
      - "18443:8443"
      - "18080:8080"
    depends_on:
      - zookeeper1
      - zookeeper2
      - zookeeper3
      - nifi-registry
      # - tls-toolkit

    networks:
      nifi_network:
        ipv4_address: ************
    volumes:
      - nifi1_conf:/opt/nifi/nifi-current/conf
      - nifi1_content_repository:/opt/nifi/nifi-current/content_repository
      - nifi1_database_repository:/opt/nifi/nifi-current/database_repository
      - nifi1_flowfile_repository:/opt/nifi/nifi-current/flowfile_repository
      - nifi1_provenance_repository:/opt/nifi/nifi-current/provenance_repository
      - nifi1_state:/opt/nifi/nifi-current/state
      - nifi1_logs:/opt/nifi/nifi-current/logs
      - ./data/toolkit/certs:/opt/certs:ro
      # - /home/<USER>/ssl:/opt/nifi/nifi-current/certs
      # - /home/<USER>/ssl/nifi1/nifi1.properties:/opt/nifi/nifi-current/conf/nifi.properties:ro
      # - /home/<USER>/ssl/nifi1.server.keystore.jks:/opt/nifi/nifi-current/conf/server.keystore.jks:ro
      # - /home/<USER>/ssl/nifi1.server.trustore.jks:/opt/nifi/nifi-current/conf/server.trustore.jks:ro
    entrypoint: >
      /bin/sh -c "
        sleep 10
        whoami
        # Copy certificates to the conf directory
        # cp /opt/nifi/nifi-current/certs/nifi1.server.keystore.jks /opt/nifi/nifi-current/conf/
        # cp /opt/nifi/nifi-current/certs/nifi1.server.truststore.jks /opt/nifi/nifi-current/conf/
        # ls /opt/nifi/nifi-current/conf/
        # sed -i 's|<property name=\"Connect String\">.*</property>|<property name=\"Connect String\">zookeeper1:2181,zookeeper2:2181,zookeeper3:2181</property>|g' \
        # /opt/nifi/nifi-current/conf/state-management.xml

        # Start NiFi
        /opt/nifi/scripts/start.sh
      "

  nifi2:
    image: apache/nifi:2.4.0
    hostname: nifi2
    container_name: nifi2
    user: nifi
    #mem_limit: 2g
    #cpus: 1.5
    environment:
      NIFI_WEB_HTTPS_PORT: "8443"
      NIFI_CLUSTER_IS_NODE: "true"
      NIFI_ZK_CONNECT_STRING: "zookeeper1:2181,zookeeper2:2181,zookeeper3:2181"
      NIFI_ELECTION_MAX_WAIT: "1 min"
      NIFI_ELECTION_MAX_CANDIDATES: 1
      NIFI_SENSITIVE_PROPS_KEY: "MySecureKey123!"
      NIFI_CLUSTER_NODE_PROTOCOL_PORT: "8080"
      NIFI_WEB_PROXY_HOST: "nifi1:18443,nifi1:8443,nifi1,nifi2:18444,nifi2:8443,nifi2,nifi3:18445,nifi3:8443,nifi3,nifi:18443,nifi:18444,nifi:18445,localhost:18443,localhost:18444,localhost:18445,localhost:8080"
      NIFI_STATE_MANAGEMENT_EMBEDDED_ZOOKEEPER_START: "false"

      # Communication Security
      # NIFI_REMOTE_INPUT_SECURE: "true"

      # Authentication Configuration
      SINGLE_USER_CREDENTIALS_USERNAME: "nifi"
      SINGLE_USER_CREDENTIALS_PASSWORD: "nifipassword"
      NIFI_SECURITY_USER_AUTHORIZER: "single-user-authorizer"
      NIFI_SECURITY_USER_LOGIN_IDENTITY_PROVIDER: "single-user-provider"
      INITIAL_ADMIN_IDENTITY: admin
      # TLS Configuration
      AUTH: "tls"
      KEYSTORE_TYPE: "JKS"
      KEYSTORE_PASSWORD: "password123"
      KEY_PASSWORD: "password123"
      TRUSTSTORE_TYPE: "JKS"
      TRUSTSTORE_PASSWORD: "password123"
      # NIFI_CLUSTER_PROTOCOL_IS_SECURE: "true"
      # NIFI_WEB_HTTPS_NETWORK_INTERFACE_DEFAULT: ""

      NIFI_CLUSTER_ADDRESS: "nifi2"
      NIFI_CLUSTER_NODE_ADDRESS: "nifi2"
      NIFI_WEB_HTTPS_HOST: "nifi2"
      KEYSTORE_PATH: "/opt/certs/nifi2/keystore.jks"
      TRUSTSTORE_PATH: "/opt/certs/nifi2/truststore.jks"

      # Registry Configuration
      NIFI_REGISTRY_URL: "http://nifi-registry:18080"

    ports:
      - "18444:8443"
      - "18081:8080"
    depends_on:
      - zookeeper1
      - zookeeper2
      - zookeeper3
      # - tls-toolkit
      - nifi-registry
    networks:
      nifi_network:
        ipv4_address: ************
    volumes:
      - nifi2_conf:/opt/nifi/nifi-current/conf
      - nifi2_content_repository:/opt/nifi/nifi-current/content_repository
      - nifi2_database_repository:/opt/nifi/nifi-current/database_repository
      - nifi2_flowfile_repository:/opt/nifi/nifi-current/flowfile_repository
      - nifi2_provenance_repository:/opt/nifi/nifi-current/provenance_repository
      - nifi2_state:/opt/nifi/nifi-current/state
      - nifi2_logs:/opt/nifi/nifi-current/logs
      - ./data/toolkit/certs:/opt/certs:ro
      # - /home/<USER>/ssl:/opt/nifi/nifi-current/certs
      # - /home/<USER>/ssl/nifi2/nifi2.properties:/opt/nifi/nifi-current/conf/nifi.properties:ro
      # - /home/<USER>/ssl/nifi2.server.keystore.jks:/opt/nifi/nifi-current/conf/server.keystore.jks:ro
      # - /home/<USER>/ssl/nifi2.server.trustore.jks:/opt/nifi/nifi-current/conf/server.trustore.jks:ro
    entrypoint: >
      /bin/sh -c "
        sleep 10
        whoami
        # Copy certificates to the conf directory
        # cp /opt/nifi/nifi-current/certs/nifi1.server.keystore.jks /opt/nifi/nifi-current/conf/
        # cp /opt/nifi/nifi-current/certs/nifi1.server.truststore.jks /opt/nifi/nifi-current/conf/
        # ls /opt/nifi/nifi-current/conf/
        # sed -i 's|<property name=\"Connect String\">.*</property>|<property name=\"Connect String\">zookeeper1:2181,zookeeper2:2181,zookeeper3:2181</property>|g' \
        # /opt/nifi/nifi-current/conf/state-management.xml

        # Start NiFi
        /opt/nifi/scripts/start.sh
      "

  nifi3:
    image: apache/nifi:2.4.0
    hostname: nifi3
    container_name: nifi3
    #mem_limit: 2g
    #cpus: 1.5
    user: nifi
    environment:
      NIFI_WEB_HTTPS_PORT: "8443"
      NIFI_CLUSTER_IS_NODE: "true"
      NIFI_ZK_CONNECT_STRING: "zookeeper1:2181,zookeeper2:2181,zookeeper3:2181"
      NIFI_ELECTION_MAX_WAIT: "1 min"
      NIFI_ELECTION_MAX_CANDIDATES: 1
      NIFI_SENSITIVE_PROPS_KEY: "MySecureKey123!"
      NIFI_CLUSTER_NODE_PROTOCOL_PORT: "8080"
      NIFI_WEB_PROXY_HOST: "nifi1:18443,nifi1:8443,nifi1,nifi2:18444,nifi2:8443,nifi2,nifi3:18445,nifi3:8443,nifi3,nifi:18443,nifi:18444,nifi:18445,localhost:18443,localhost:18444,localhost:18445,localhost:8080"
      NIFI_STATE_MANAGEMENT_EMBEDDED_ZOOKEEPER_START: "false"

      # Communication Security
      # NIFI_REMOTE_INPUT_SECURE: "true"

      # Authentication Configuration
      SINGLE_USER_CREDENTIALS_USERNAME: "nifi"
      SINGLE_USER_CREDENTIALS_PASSWORD: "nifipassword"
      NIFI_SECURITY_USER_AUTHORIZER: "single-user-authorizer"
      NIFI_SECURITY_USER_LOGIN_IDENTITY_PROVIDER: "single-user-provider"
      INITIAL_ADMIN_IDENTITY: admin
      # TLS Configuration
      AUTH: "tls"
      KEYSTORE_TYPE: "JKS"
      KEYSTORE_PASSWORD: "password123"
      KEY_PASSWORD: "password123"
      TRUSTSTORE_TYPE: "JKS"
      TRUSTSTORE_PASSWORD: "password123"
      # NIFI_CLUSTER_PROTOCOL_IS_SECURE: "true"
      # NIFI_WEB_HTTPS_NETWORK_INTERFACE_DEFAULT: ""

      NIFI_CLUSTER_ADDRESS: "nifi3"
      NIFI_CLUSTER_NODE_ADDRESS: "nifi3"
      NIFI_WEB_HTTPS_HOST: "nifi3"
      KEYSTORE_PATH: "/opt/certs/nifi3/keystore.jks"
      TRUSTSTORE_PATH: "/opt/certs/nifi3/truststore.jks"

      # Registry Configuration
      NIFI_REGISTRY_URL: "http://nifi-registry:18080"

    ports:
      - "18445:8443"
      - "18082:8080"
    depends_on:
      - zookeeper1
      - zookeeper2
      - zookeeper3
      # - tls-toolkit
      - nifi-registry
    networks:
      nifi_network:
        ipv4_address: ************
    volumes:
      - nifi3_conf:/opt/nifi/nifi-current/conf
      - nifi3_content_repository:/opt/nifi/nifi-current/content_repository
      - nifi3_database_repository:/opt/nifi/nifi-current/database_repository
      - nifi3_flowfile_repository:/opt/nifi/nifi-current/flowfile_repository
      - nifi3_provenance_repository:/opt/nifi/nifi-current/provenance_repository
      - nifi3_state:/opt/nifi/nifi-current/state
      - nifi3_logs:/opt/nifi/nifi-current/logs
      - ./data/toolkit/certs:/opt/certs:ro
      # - /home/<USER>/ssl:/opt/nifi/nifi-current/certs
      # - /home/<USER>/ssl/nifi3/nifi3.properties:/opt/nifi/nifi-current/conf/nifi.properties:ro
      # - /home/<USER>/ssl/nifi3.server.keystore.jks:/opt/nifi/nifi-current/conf/server.keystore.jks:ro
      # - /home/<USER>/ssl/nifi3.server.trustore.jks:/opt/nifi/nifi-current/conf/server.trustore.jks:ro
    entrypoint: >
      /bin/sh -c "
        sleep 10
        whoami
        # Copy certificates to the conf directory
        # cp /opt/nifi/nifi-current/certs/nifi1.server.keystore.jks /opt/nifi/nifi-current/conf/
        # cp /opt/nifi/nifi-current/certs/nifi1.server.truststore.jks /opt/nifi/nifi-current/conf/
        # ls /opt/nifi/nifi-current/conf/
        # sed -i 's|<property name=\"Connect String\">.*</property>|<property name=\"Connect String\">zookeeper1:2181,zookeeper2:2181,zookeeper3:2181</property>|g' \
        # /opt/nifi/nifi-current/conf/state-management.xml

        # Start NiFi
        /opt/nifi/scripts/start.sh
      "

  tls-toolkit:
    image: apache/nifi:1.28.1
    hostname: nifi-toolkit
    container_name: nifi-toolkit
    mem_limit: 1g
    cpus: 0.5
    entrypoint: [
      "bash",
      "-c",
      "/opt/nifi/nifi-toolkit-current/bin/tls-toolkit.sh standalone -O -o /opt/certs -n 'nifi1,nifi2,nifi3' -P password123 -K password123 -S password123 -B password123; chown -R nifi:nifi /opt/certs;chown -Rf nifi:nifi /opt/certs;ls /opt/certs/ -lrt"
    ]
    # entrypoint: >
    #   /bin/sh -c "
    #   /opt/nifi/nifi-toolkit-current/bin/tls-toolkit.sh standalone -n 'nifi1,nifi2,nifi3' -C 'CN=admin,OU=NIFI' -O -o /opt/certs -S password123 -K password123 -P password123 -B password123; chown -Rf nifi:nifi /opt/certs; ls "
    volumes:
      - "./data/toolkit/certs:/opt/certs"
    user: root
    networks:
      nifi_network:
        ipv4_address: ************

  kafka1:
    image: 'docker.io/bitnami/kafka:latest'
    hostname: kafka1
    container_name: kafka1
    # mem_limit: 2g
    # cpus: 1.0
    environment:
      # ZooKeeper Configuration
      KAFKA_CFG_ZOOKEEPER_CONNECT: "zookeeper1:2181,zookeeper2:2181,zookeeper3:2181"

      # Force ZooKeeper mode (disable KRaft)
      KAFKA_ENABLE_KRAFT: "false"
      KAFKA_CFG_PROCESS_ROLES: ""

      # Broker Configuration
      KAFKA_CFG_BROKER_ID: 1
      KAFKA_CFG_NUM_NETWORK_THREADS: 3
      KAFKA_CFG_NUM_IO_THREADS: 8
      KAFKA_CFG_SOCKET_SEND_BUFFER_BYTES: 102400
      KAFKA_CFG_SOCKET_RECEIVE_BUFFER_BYTES: 102400
      KAFKA_CFG_SOCKET_REQUEST_MAX_BYTES: 104857600

      # Log Configuration
      KAFKA_CFG_LOG_DIRS: "/bitnami/kafka/data"
      KAFKA_CFG_NUM_PARTITIONS: 3
      KAFKA_CFG_LOG_RETENTION_HOURS: 168
      KAFKA_CFG_LOG_SEGMENT_BYTES: 1073741824
      KAFKA_CFG_LOG_RETENTION_CHECK_INTERVAL_MS: 300000

      # Listener Configuration with SASL
      KAFKA_CFG_LISTENERS: "SASL_PLAINTEXT://:9092"
      KAFKA_CFG_ADVERTISED_LISTENERS: "SASL_PLAINTEXT://kafka1:9092"
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: "SASL_PLAINTEXT:SASL_PLAINTEXT"

      # SASL Configuration
      KAFKA_CFG_SASL_ENABLED_MECHANISMS: "PLAIN"
      KAFKA_CFG_SASL_MECHANISM_INTER_BROKER_PROTOCOL: "PLAIN"
      KAFKA_CFG_SECURITY_INTER_BROKER_PROTOCOL: "SASL_PLAINTEXT"

      # Authentication
      KAFKA_INTER_BROKER_USER: "admin"
      KAFKA_INTER_BROKER_PASSWORD: "adminpassword"
      KAFKA_CFG_SUPER_USERS: "User:admin"

      # Client Authentication
      KAFKA_CLIENT_USERS: "admin"
      KAFKA_CLIENT_PASSWORDS: "adminpassword"

      # Cluster Configuration
      KAFKA_CFG_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_CFG_MIN_INSYNC_REPLICAS: 2
      KAFKA_CFG_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_CFG_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_CFG_TRANSACTION_STATE_LOG_MIN_ISR: 2

    ports:
      - "19092:9092"
    depends_on:
      - zookeeper1
      - zookeeper2
      - zookeeper3
    networks:
      nifi_network:
        ipv4_address: ************
    volumes:
      - kafka1_data:/bitnami/kafka/data

  kafka2:
    image: 'docker.io/bitnami/kafka:latest'
    hostname: kafka2
    container_name: kafka2
    # mem_limit: 2g
    # cpus: 1.0
    environment:
      # ZooKeeper Configuration
      KAFKA_CFG_ZOOKEEPER_CONNECT: "zookeeper1:2181,zookeeper2:2181,zookeeper3:2181"

      # Force ZooKeeper mode (disable KRaft)
      KAFKA_ENABLE_KRAFT: "false"
      KAFKA_CFG_PROCESS_ROLES: ""

      # Broker Configuration
      KAFKA_CFG_BROKER_ID: 2
      KAFKA_CFG_NUM_NETWORK_THREADS: 3
      KAFKA_CFG_NUM_IO_THREADS: 8
      KAFKA_CFG_SOCKET_SEND_BUFFER_BYTES: 102400
      KAFKA_CFG_SOCKET_RECEIVE_BUFFER_BYTES: 102400
      KAFKA_CFG_SOCKET_REQUEST_MAX_BYTES: 104857600

      # Log Configuration
      KAFKA_CFG_LOG_DIRS: "/bitnami/kafka/data"
      KAFKA_CFG_NUM_PARTITIONS: 3
      KAFKA_CFG_LOG_RETENTION_HOURS: 168
      KAFKA_CFG_LOG_SEGMENT_BYTES: 1073741824
      KAFKA_CFG_LOG_RETENTION_CHECK_INTERVAL_MS: 300000

      # Listener Configuration with SASL
      KAFKA_CFG_LISTENERS: "SASL_PLAINTEXT://:9092"
      KAFKA_CFG_ADVERTISED_LISTENERS: "SASL_PLAINTEXT://kafka2:9092"
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: "SASL_PLAINTEXT:SASL_PLAINTEXT"

      # SASL Configuration
      KAFKA_CFG_SASL_ENABLED_MECHANISMS: "PLAIN"
      KAFKA_CFG_SASL_MECHANISM_INTER_BROKER_PROTOCOL: "PLAIN"
      KAFKA_CFG_SECURITY_INTER_BROKER_PROTOCOL: "SASL_PLAINTEXT"

      # Authentication
      KAFKA_INTER_BROKER_USER: "admin"
      KAFKA_INTER_BROKER_PASSWORD: "adminpassword"
      KAFKA_CFG_SUPER_USERS: "User:admin"

      # Client Authentication
      KAFKA_CLIENT_USERS: "admin"
      KAFKA_CLIENT_PASSWORDS: "adminpassword"

      # Cluster Configuration
      KAFKA_CFG_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_CFG_MIN_INSYNC_REPLICAS: 2
      KAFKA_CFG_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_CFG_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_CFG_TRANSACTION_STATE_LOG_MIN_ISR: 2

    ports:
      - "19093:9092"
    depends_on:
      - zookeeper1
      - zookeeper2
      - zookeeper3
    networks:
      nifi_network:
        ipv4_address: ************
    volumes:
      - kafka2_data:/bitnami/kafka/data

  kafka3:
    image: 'docker.io/bitnami/kafka:latest'
    hostname: kafka3
    container_name: kafka3
    # mem_limit: 2g
    # cpus: 1.0
    environment:
      # ZooKeeper Configuration
      KAFKA_CFG_ZOOKEEPER_CONNECT: "zookeeper1:2181,zookeeper2:2181,zookeeper3:2181"

      # Force ZooKeeper mode (disable KRaft)
      KAFKA_ENABLE_KRAFT: "false"
      KAFKA_CFG_PROCESS_ROLES: ""

      # Broker Configuration
      KAFKA_CFG_BROKER_ID: 3
      KAFKA_CFG_NUM_NETWORK_THREADS: 3
      KAFKA_CFG_NUM_IO_THREADS: 8
      KAFKA_CFG_SOCKET_SEND_BUFFER_BYTES: 102400
      KAFKA_CFG_SOCKET_RECEIVE_BUFFER_BYTES: 102400
      KAFKA_CFG_SOCKET_REQUEST_MAX_BYTES: 104857600

      # Log Configuration
      KAFKA_CFG_LOG_DIRS: "/bitnami/kafka/data"
      KAFKA_CFG_NUM_PARTITIONS: 3
      KAFKA_CFG_LOG_RETENTION_HOURS: 168
      KAFKA_CFG_LOG_SEGMENT_BYTES: 1073741824
      KAFKA_CFG_LOG_RETENTION_CHECK_INTERVAL_MS: 300000

      # Listener Configuration with SASL
      KAFKA_CFG_LISTENERS: "SASL_PLAINTEXT://:9092"
      KAFKA_CFG_ADVERTISED_LISTENERS: "SASL_PLAINTEXT://kafka3:9092"
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: "SASL_PLAINTEXT:SASL_PLAINTEXT"

      # SASL Configuration
      KAFKA_CFG_SASL_ENABLED_MECHANISMS: "PLAIN"
      KAFKA_CFG_SASL_MECHANISM_INTER_BROKER_PROTOCOL: "PLAIN"
      KAFKA_CFG_SECURITY_INTER_BROKER_PROTOCOL: "SASL_PLAINTEXT"

      # Authentication
      KAFKA_INTER_BROKER_USER: "admin"
      KAFKA_INTER_BROKER_PASSWORD: "adminpassword"
      KAFKA_CFG_SUPER_USERS: "User:admin"

      # Client Authentication
      KAFKA_CLIENT_USERS: "admin"
      KAFKA_CLIENT_PASSWORDS: "adminpassword"

      # Cluster Configuration
      KAFKA_CFG_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_CFG_MIN_INSYNC_REPLICAS: 2
      KAFKA_CFG_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_CFG_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_CFG_TRANSACTION_STATE_LOG_MIN_ISR: 2

    ports:
      - "19094:9092"
    depends_on:
      - zookeeper1
      - zookeeper2
      - zookeeper3
    networks:
      nifi_network:
        ipv4_address: ************
    volumes:
      - kafka3_data:/bitnami/kafka/data

  kafka-ui:
    image: 'provectuslabs/kafka-ui:latest'
    hostname: kafka-ui
    container_name: kafka-ui
    environment:
      KAFKA_CLUSTERS_0_NAME: "kafka-cluster"
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: "kafka1:9092,kafka2:9092,kafka3:9092"
      KAFKA_CLUSTERS_0_ZOOKEEPER: "zookeeper1:2181,zookeeper2:2181,zookeeper3:2181"
      KAFKA_CLUSTERS_0_PROPERTIES_SECURITY_PROTOCOL: "SASL_PLAINTEXT"
      KAFKA_CLUSTERS_0_PROPERTIES_SASL_MECHANISM: "PLAIN"
      KAFKA_CLUSTERS_0_PROPERTIES_SASL_JAAS_CONFIG: 'org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="adminpassword";'
    ports:
      - "19090:8080"
    depends_on:
      - kafka1
      - kafka2
      - kafka3
    networks:
      nifi_network:
        ipv4_address: ************

volumes:
  # nifi_certs:
  #   driver: local
  zk_data1:
    driver: local
  zk_data2:
    driver: local
  zk_data3:
    driver: local
  zk_conf1:
    driver: local
  zk_conf2:
    driver: local
  zk_conf3:
    driver: local
  certs:
    driver: local
  nifi1_conf:
    driver: local
  nifi1_content_repository:
    driver: local
  nifi1_database_repository:
    driver: local
  nifi1_flowfile_repository:
    driver: local
  nifi1_provenance_repository:
    driver: local
  nifi1_state:
    driver: local
  nifi1_logs:
    driver: local
  nifi2_conf:
    driver: local
  nifi2_content_repository:
    driver: local
  nifi2_database_repository:
    driver: local
  nifi2_flowfile_repository:
    driver: local
  nifi2_provenance_repository:
    driver: local
  nifi2_state:
    driver: local
  nifi2_logs:
    driver: local
  nifi3_conf:
    driver: local
  nifi3_content_repository:
    driver: local
  nifi3_database_repository:
    driver: local
  nifi3_flowfile_repository:
    driver: local
  nifi3_provenance_repository:
    driver: local
  nifi3_state:
    driver: local
  nifi3_logs:
    driver: local
  kafka1_data:
    driver: local
  kafka2_data:
    driver: local
  kafka3_data:
    driver: local