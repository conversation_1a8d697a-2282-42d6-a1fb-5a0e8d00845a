# Kafka 客户端配置文件
# 用于连接到 SASL 认证的 Kafka 集群

# Bootstrap 服务器列表
bootstrap.servers=localhost:9092,localhost:9093,localhost:9094

# 安全协议配置
security.protocol=SASL_PLAINTEXT

# SASL 机制
sasl.mechanism=PLAIN

# JAAS 配置 - 包含用户名和密码
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="adminpassword";

# 生产者配置
# 确认级别 - all 表示等待所有副本确认
acks=all

# 重试次数
retries=3

# 批处理大小 (字节)
batch.size=16384

# 延迟时间 (毫秒)
linger.ms=1

# 缓冲区大小 (字节)
buffer.memory=33554432

# 键和值的序列化器
key.serializer=org.apache.kafka.common.serialization.StringSerializer
value.serializer=org.apache.kafka.common.serialization.StringSerializer

# 消费者配置
# 消费者组ID (需要根据实际情况修改)
group.id=test-consumer-group

# 自动提交偏移量
enable.auto.commit=true

# 自动提交间隔 (毫秒)
auto.commit.interval.ms=1000

# 会话超时 (毫秒)
session.timeout.ms=30000

# 键和值的反序列化器
key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
value.deserializer=org.apache.kafka.common.serialization.StringDeserializer

# 从最早的偏移量开始消费 (earliest) 或从最新的偏移量开始 (latest)
auto.offset.reset=earliest
