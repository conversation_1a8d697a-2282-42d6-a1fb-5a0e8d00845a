#!/bin/bash

# Kafka 集群管理脚本
# 使用方法: ./kafka-scripts.sh [command] [options]

KAFKA_CONTAINER="kafka1"
BOOTSTRAP_SERVERS="kafka1:9092,kafka2:9092,kafka3:9092"
CONFIG_FILE="/opt/bitnami/kafka/config/client.properties"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印帮助信息
print_help() {
    echo -e "${BLUE}Kafka 集群管理脚本${NC}"
    echo ""
    echo "使用方法: $0 [command] [options]"
    echo ""
    echo "可用命令:"
    echo "  list-topics                    - 列出所有主题"
    echo "  create-topic <name> [partitions] [replication] - 创建主题"
    echo "  delete-topic <name>            - 删除主题"
    echo "  describe-topic <name>          - 描述主题详情"
    echo "  list-consumer-groups           - 列出消费者组"
    echo "  describe-consumer-group <name> - 描述消费者组"
    echo "  produce <topic>                - 生产消息到主题"
    echo "  consume <topic> [group]        - 消费主题消息"
    echo "  cluster-info                   - 显示集群信息"
    echo "  broker-list                    - 列出所有 broker"
    echo "  test-connection                - 测试连接"
    echo "  help                           - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 create-topic test-topic 3 3"
    echo "  $0 produce test-topic"
    echo "  $0 consume test-topic my-group"
}

# 检查 Docker 容器是否运行
check_container() {
    if ! docker ps | grep -q $KAFKA_CONTAINER; then
        echo -e "${RED}错误: Kafka 容器 $KAFKA_CONTAINER 未运行${NC}"
        exit 1
    fi
}

# 执行 Kafka 命令
exec_kafka_cmd() {
    docker exec -it $KAFKA_CONTAINER "$@"
}

# 列出所有主题
list_topics() {
    echo -e "${GREEN}列出所有主题:${NC}"
    exec_kafka_cmd kafka-topics.sh --list --bootstrap-server $BOOTSTRAP_SERVERS --command-config $CONFIG_FILE
}

# 创建主题
create_topic() {
    local topic_name=$1
    local partitions=${2:-3}
    local replication=${3:-3}
    
    if [ -z "$topic_name" ]; then
        echo -e "${RED}错误: 请提供主题名称${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}创建主题: $topic_name (分区: $partitions, 副本: $replication)${NC}"
    exec_kafka_cmd kafka-topics.sh --create \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --topic $topic_name \
        --partitions $partitions \
        --replication-factor $replication \
        --command-config $CONFIG_FILE
}

# 删除主题
delete_topic() {
    local topic_name=$1
    
    if [ -z "$topic_name" ]; then
        echo -e "${RED}错误: 请提供主题名称${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}删除主题: $topic_name${NC}"
    read -p "确认删除主题 '$topic_name'? (y/N): " confirm
    if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
        exec_kafka_cmd kafka-topics.sh --delete \
            --bootstrap-server $BOOTSTRAP_SERVERS \
            --topic $topic_name \
            --command-config $CONFIG_FILE
    else
        echo "取消删除操作"
    fi
}

# 描述主题
describe_topic() {
    local topic_name=$1
    
    if [ -z "$topic_name" ]; then
        echo -e "${RED}错误: 请提供主题名称${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}主题详情: $topic_name${NC}"
    exec_kafka_cmd kafka-topics.sh --describe \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --topic $topic_name \
        --command-config $CONFIG_FILE
}

# 列出消费者组
list_consumer_groups() {
    echo -e "${GREEN}列出所有消费者组:${NC}"
    exec_kafka_cmd kafka-consumer-groups.sh --list \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $CONFIG_FILE
}

# 描述消费者组
describe_consumer_group() {
    local group_name=$1
    
    if [ -z "$group_name" ]; then
        echo -e "${RED}错误: 请提供消费者组名称${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}消费者组详情: $group_name${NC}"
    exec_kafka_cmd kafka-consumer-groups.sh --describe \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --group $group_name \
        --command-config $CONFIG_FILE
}

# 生产消息
produce_messages() {
    local topic_name=$1
    
    if [ -z "$topic_name" ]; then
        echo -e "${RED}错误: 请提供主题名称${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}开始生产消息到主题: $topic_name${NC}"
    echo -e "${YELLOW}输入消息 (按 Ctrl+C 退出):${NC}"
    exec_kafka_cmd kafka-console-producer.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --topic $topic_name \
        --producer.config $CONFIG_FILE
}

# 消费消息
consume_messages() {
    local topic_name=$1
    local group_name=${2:-"console-consumer-$(date +%s)"}
    
    if [ -z "$topic_name" ]; then
        echo -e "${RED}错误: 请提供主题名称${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}开始消费主题: $topic_name (消费者组: $group_name)${NC}"
    echo -e "${YELLOW}按 Ctrl+C 退出${NC}"
    exec_kafka_cmd kafka-console-consumer.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --topic $topic_name \
        --group $group_name \
        --from-beginning \
        --consumer.config $CONFIG_FILE
}

# 显示集群信息
cluster_info() {
    echo -e "${GREEN}Kafka 集群信息:${NC}"
    exec_kafka_cmd kafka-broker-api-versions.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $CONFIG_FILE
}

# 列出 broker
broker_list() {
    echo -e "${GREEN}Broker 列表:${NC}"
    exec_kafka_cmd kafka-metadata-shell.sh \
        --snapshot /opt/bitnami/kafka/data/__cluster_metadata-0/00000000000000000000.log \
        --print-brokers 2>/dev/null || echo "使用替代方法获取 broker 信息..."
    
    # 替代方法：通过主题元数据获取 broker 信息
    echo -e "${BLUE}通过主题元数据获取 broker 信息:${NC}"
    exec_kafka_cmd kafka-topics.sh --describe \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $CONFIG_FILE | head -10
}

# 测试连接
test_connection() {
    echo -e "${GREEN}测试 Kafka 集群连接...${NC}"
    
    # 测试连接
    if exec_kafka_cmd kafka-broker-api-versions.sh \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --command-config $CONFIG_FILE >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 连接成功${NC}"
        
        # 显示集群基本信息
        echo -e "${BLUE}集群基本信息:${NC}"
        echo "Bootstrap Servers: $BOOTSTRAP_SERVERS"
        echo "认证方式: SASL_PLAINTEXT"
        echo "用户名: admin"
        
        # 显示主题数量
        topic_count=$(exec_kafka_cmd kafka-topics.sh --list \
            --bootstrap-server $BOOTSTRAP_SERVERS \
            --command-config $CONFIG_FILE 2>/dev/null | wc -l)
        echo "主题数量: $topic_count"
        
    else
        echo -e "${RED}✗ 连接失败${NC}"
        exit 1
    fi
}

# 主函数
main() {
    check_container
    
    case "$1" in
        "list-topics")
            list_topics
            ;;
        "create-topic")
            create_topic "$2" "$3" "$4"
            ;;
        "delete-topic")
            delete_topic "$2"
            ;;
        "describe-topic")
            describe_topic "$2"
            ;;
        "list-consumer-groups")
            list_consumer_groups
            ;;
        "describe-consumer-group")
            describe_consumer_group "$2"
            ;;
        "produce")
            produce_messages "$2"
            ;;
        "consume")
            consume_messages "$2" "$3"
            ;;
        "cluster-info")
            cluster_info
            ;;
        "broker-list")
            broker_list
            ;;
        "test-connection")
            test_connection
            ;;
        "help"|"")
            print_help
            ;;
        *)
            echo -e "${RED}未知命令: $1${NC}"
            print_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
