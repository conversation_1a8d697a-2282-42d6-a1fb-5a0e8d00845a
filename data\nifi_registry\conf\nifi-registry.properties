# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# web properties #
nifi.registry.web.war.directory=./lib
nifi.registry.web.http.host=nifi-registry
nifi.registry.web.http.port=18080
nifi.registry.web.https.host=
nifi.registry.web.https.port=
nifi.registry.web.https.network.interface.default=
nifi.registry.web.https.application.protocols=h2 http/1.1
nifi.registry.web.jetty.working.directory=./work/jetty
nifi.registry.web.jetty.threads=200
nifi.registry.web.should.send.server.version=true

# security properties #
nifi.registry.security.keystore=
nifi.registry.security.keystoreType=
nifi.registry.security.keystorePasswd=
nifi.registry.security.keyPasswd=
nifi.registry.security.truststore=
nifi.registry.security.truststoreType=
nifi.registry.security.truststorePasswd=
nifi.registry.security.needClientAuth=
nifi.registry.security.authorizers.configuration.file=./conf/authorizers.xml
nifi.registry.security.authorizer=managed-authorizer
nifi.registry.security.identity.providers.configuration.file=./conf/identity-providers.xml
nifi.registry.security.identity.provider=

# providers properties #
nifi.registry.providers.configuration.file=./conf/providers.xml

# registry alias properties #
nifi.registry.registry.alias.configuration.file=./conf/registry-aliases.xml

# extensions working dir #
nifi.registry.extensions.working.directory=./work/extensions

# legacy database properties, used to migrate data from original DB to new DB below
# NOTE: Users upgrading from 0.1.0 should leave these populated, but new installs after 0.1.0 should leave these empty
nifi.registry.db.directory=
nifi.registry.db.url.append=

# database properties
nifi.registry.db.url=jdbc:h2:./database/nifi-registry-primary;AUTOCOMMIT=OFF;DB_CLOSE_ON_EXIT=FALSE;LOCK_MODE=3;LOCK_TIMEOUT=25000;WRITE_DELAY=0;AUTO_SERVER=FALSE
nifi.registry.db.driver.class=org.h2.Driver
nifi.registry.db.driver.directory=/opt/nifi-registry/nifi-registry-current/database
nifi.registry.db.username=nifireg
nifi.registry.db.password=nifireg
nifi.registry.db.maxConnections=5
nifi.registry.db.sql.debug=false

# extension directories #
# Each property beginning with "nifi.registry.extension.dir." will be treated as location for an extension,
# and a class loader will be created for each location, with the system class loader as the parent
#
#nifi.registry.extension.dir.1=/path/to/extension1
#nifi.registry.extension.dir.2=/path/to/extension2

nifi.registry.extension.dir.aws=./ext/aws/lib

# Identity Mapping Properties #
# These properties allow normalizing user identities such that identities coming from different identity providers
# (certificates, LDAP, Kerberos) can be treated the same internally in NiFi. The following example demonstrates normalizing
# DNs from certificates and principals from Kerberos into a common identity string:
#
# nifi.registry.security.identity.mapping.pattern.dn=^CN=(.*?), OU=(.*?), O=(.*?), L=(.*?), ST=(.*?), C=(.*?)$
# nifi.registry.security.identity.mapping.value.dn=$1@$2
# nifi.registry.security.identity.mapping.transform.dn=NONE

# nifi.registry.security.identity.mapping.pattern.kerb=^(.*?)/instance@(.*?)$
# nifi.registry.security.identity.mapping.value.kerb=$1@$2
# nifi.registry.security.identity.mapping.transform.kerb=UPPER

# Group Mapping Properties #
# These properties allow normalizing group names coming from external sources like LDAP. The following example
# lowercases any group name.
#
# nifi.registry.security.group.mapping.pattern.anygroup=^(.*)$
# nifi.registry.security.group.mapping.value.anygroup=$1
# nifi.registry.security.group.mapping.transform.anygroup=LOWER


# kerberos properties #
nifi.registry.kerberos.krb5.file=
nifi.registry.kerberos.spnego.principal=
nifi.registry.kerberos.spnego.keytab.location=
nifi.registry.kerberos.spnego.authentication.expiration=12 hours

# OIDC #
nifi.registry.security.user.oidc.discovery.url=
nifi.registry.security.user.oidc.connect.timeout=
nifi.registry.security.user.oidc.read.timeout=
nifi.registry.security.user.oidc.client.id=
nifi.registry.security.user.oidc.client.secret=
nifi.registry.security.user.oidc.preferred.jwsalgorithm=
nifi.registry.security.user.oidc.claim.groups=groups

# revision management #
# This feature should remain disabled until a future NiFi release that supports the revision API changes
nifi.registry.revisions.enabled=false