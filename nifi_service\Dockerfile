FROM m.daocloud.io/docker.io/apache/nifi:2.4.0

USER root

# 配置中国镜像源 (Ubuntu/Debian)
RUN sed -i '<EMAIL>@mirrors.aliyun.com@g' /etc/apt/sources.list.d/debian.sources
# 配置 pip 中国镜像源
RUN mkdir -p /root/.pip && \
    echo '[global]' > /root/.pip/pip.conf && \
    echo 'index-url = https://pypi.tuna.tsinghua.edu.cn/simple' >> /root/.pip/pip.conf && \
    echo 'trusted-host = pypi.tuna.tsinghua.edu.cn' >> /root/.pip/pip.conf

# 安装软件包
RUN apt-get update && apt-get -y upgrade && \
    apt-get -y install python3 python3-pip && \
    PIP_BREAK_SYSTEM_PACKAGES=1 pip3 install --no-cache-dir pymongo pandas && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

USER nifi