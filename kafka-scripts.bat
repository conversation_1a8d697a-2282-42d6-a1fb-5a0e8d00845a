@echo off
REM Kafka 集群管理脚本 (Windows 版本)
REM 使用方法: kafka-scripts.bat [command] [options]

setlocal enabledelayedexpansion

set KAFKA_CONTAINER=kafka1
set BOOTSTRAP_SERVERS=kafka1:9092,kafka2:9092,kafka3:9092
set CONFIG_FILE=/opt/bitnami/kafka/config/client.properties

if "%1"=="" goto :help
if "%1"=="help" goto :help
if "%1"=="list-topics" goto :list_topics
if "%1"=="create-topic" goto :create_topic
if "%1"=="delete-topic" goto :delete_topic
if "%1"=="describe-topic" goto :describe_topic
if "%1"=="list-consumer-groups" goto :list_consumer_groups
if "%1"=="describe-consumer-group" goto :describe_consumer_group
if "%1"=="produce" goto :produce
if "%1"=="consume" goto :consume
if "%1"=="cluster-info" goto :cluster_info
if "%1"=="broker-list" goto :broker_list
if "%1"=="test-connection" goto :test_connection

echo 未知命令: %1
goto :help

:help
echo Kafka 集群管理脚本
echo.
echo 使用方法: %0 [command] [options]
echo.
echo 可用命令:
echo   list-topics                    - 列出所有主题
echo   create-topic ^<name^> [partitions] [replication] - 创建主题
echo   delete-topic ^<name^>            - 删除主题
echo   describe-topic ^<name^>          - 描述主题详情
echo   list-consumer-groups           - 列出消费者组
echo   describe-consumer-group ^<name^> - 描述消费者组
echo   produce ^<topic^>                - 生产消息到主题
echo   consume ^<topic^> [group]        - 消费主题消息
echo   cluster-info                   - 显示集群信息
echo   broker-list                    - 列出所有 broker
echo   test-connection                - 测试连接
echo   help                           - 显示此帮助信息
echo.
echo 示例:
echo   %0 create-topic test-topic 3 3
echo   %0 produce test-topic
echo   %0 consume test-topic my-group
goto :end

:check_container
docker ps | findstr %KAFKA_CONTAINER% >nul
if errorlevel 1 (
    echo 错误: Kafka 容器 %KAFKA_CONTAINER% 未运行
    exit /b 1
)
goto :eof

:list_topics
call :check_container
echo 列出所有主题:
docker exec -it %KAFKA_CONTAINER% kafka-topics.sh --list --bootstrap-server %BOOTSTRAP_SERVERS% --command-config %CONFIG_FILE%
goto :end

:create_topic
call :check_container
if "%2"=="" (
    echo 错误: 请提供主题名称
    exit /b 1
)
set topic_name=%2
set partitions=%3
set replication=%4
if "%partitions%"=="" set partitions=3
if "%replication%"=="" set replication=3

echo 创建主题: %topic_name% ^(分区: %partitions%, 副本: %replication%^)
docker exec -it %KAFKA_CONTAINER% kafka-topics.sh --create --bootstrap-server %BOOTSTRAP_SERVERS% --topic %topic_name% --partitions %partitions% --replication-factor %replication% --command-config %CONFIG_FILE%
goto :end

:delete_topic
call :check_container
if "%2"=="" (
    echo 错误: 请提供主题名称
    exit /b 1
)
set topic_name=%2
echo 删除主题: %topic_name%
set /p confirm=确认删除主题 '%topic_name%'? (y/N): 
if /i "%confirm%"=="y" (
    docker exec -it %KAFKA_CONTAINER% kafka-topics.sh --delete --bootstrap-server %BOOTSTRAP_SERVERS% --topic %topic_name% --command-config %CONFIG_FILE%
) else (
    echo 取消删除操作
)
goto :end

:describe_topic
call :check_container
if "%2"=="" (
    echo 错误: 请提供主题名称
    exit /b 1
)
set topic_name=%2
echo 主题详情: %topic_name%
docker exec -it %KAFKA_CONTAINER% kafka-topics.sh --describe --bootstrap-server %BOOTSTRAP_SERVERS% --topic %topic_name% --command-config %CONFIG_FILE%
goto :end

:list_consumer_groups
call :check_container
echo 列出所有消费者组:
docker exec -it %KAFKA_CONTAINER% kafka-consumer-groups.sh --list --bootstrap-server %BOOTSTRAP_SERVERS% --command-config %CONFIG_FILE%
goto :end

:describe_consumer_group
call :check_container
if "%2"=="" (
    echo 错误: 请提供消费者组名称
    exit /b 1
)
set group_name=%2
echo 消费者组详情: %group_name%
docker exec -it %KAFKA_CONTAINER% kafka-consumer-groups.sh --describe --bootstrap-server %BOOTSTRAP_SERVERS% --group %group_name% --command-config %CONFIG_FILE%
goto :end

:produce
call :check_container
if "%2"=="" (
    echo 错误: 请提供主题名称
    exit /b 1
)
set topic_name=%2
echo 开始生产消息到主题: %topic_name%
echo 输入消息 ^(按 Ctrl+C 退出^):
docker exec -it %KAFKA_CONTAINER% kafka-console-producer.sh --bootstrap-server %BOOTSTRAP_SERVERS% --topic %topic_name% --producer.config %CONFIG_FILE%
goto :end

:consume
call :check_container
if "%2"=="" (
    echo 错误: 请提供主题名称
    exit /b 1
)
set topic_name=%2
set group_name=%3
if "%group_name%"=="" (
    for /f %%i in ('powershell -command "Get-Date -UFormat %%s"') do set timestamp=%%i
    set group_name=console-consumer-!timestamp!
)
echo 开始消费主题: %topic_name% ^(消费者组: %group_name%^)
echo 按 Ctrl+C 退出
docker exec -it %KAFKA_CONTAINER% kafka-console-consumer.sh --bootstrap-server %BOOTSTRAP_SERVERS% --topic %topic_name% --group %group_name% --from-beginning --consumer.config %CONFIG_FILE%
goto :end

:cluster_info
call :check_container
echo Kafka 集群信息:
docker exec -it %KAFKA_CONTAINER% kafka-broker-api-versions.sh --bootstrap-server %BOOTSTRAP_SERVERS% --command-config %CONFIG_FILE%
goto :end

:broker_list
call :check_container
echo Broker 列表:
echo 通过主题元数据获取 broker 信息:
docker exec -it %KAFKA_CONTAINER% kafka-topics.sh --describe --bootstrap-server %BOOTSTRAP_SERVERS% --command-config %CONFIG_FILE%
goto :end

:test_connection
call :check_container
echo 测试 Kafka 集群连接...

docker exec -it %KAFKA_CONTAINER% kafka-broker-api-versions.sh --bootstrap-server %BOOTSTRAP_SERVERS% --command-config %CONFIG_FILE% >nul 2>&1
if errorlevel 1 (
    echo ✗ 连接失败
    exit /b 1
) else (
    echo ✓ 连接成功
    echo.
    echo 集群基本信息:
    echo Bootstrap Servers: %BOOTSTRAP_SERVERS%
    echo 认证方式: SASL_PLAINTEXT
    echo 用户名: admin
    
    echo 获取主题数量...
    for /f %%i in ('docker exec %KAFKA_CONTAINER% kafka-topics.sh --list --bootstrap-server %BOOTSTRAP_SERVERS% --command-config %CONFIG_FILE% 2^>nul ^| find /c /v ""') do set topic_count=%%i
    echo 主题数量: !topic_count!
)
goto :end

:end
endlocal
