version: '3.8'

services:
  zookeeper1:
    image: confluentinc/cp-zookeeper:7.3.2
    hostname: zookeeper1
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_SERVERS: zookeeper1:2888:3888;zookeeper2:2888:3888;zookeeper3:2888:3888
    volumes:
      - zookeeper1_data:/var/lib/zookeeper/data
      - zookeeper1_log:/var/lib/zookeeper/log
    networks:
      - nifi_network
    deploy:
      replicas: 1
      placement:
        constraints: [node.hostname == <PHYSICAL_HOST1_HOSTNAME>] # 替换为实际主机名

  zookeeper2:
    image: confluentinc/cp-zookeeper:7.3.2
    hostname: zookeeper2
    ports:
      - "2182:2181" # 宿主机端口映射避免冲突
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_SERVER_ID: 2
      ZOOKEEPER_SERVERS: zookeeper1:2888:3888;zookeeper2:2888:3888;zookeeper3:2888:3888
    volumes:
      - zookeeper2_data:/var/lib/zookeeper/data
      - zookeeper2_log:/var/lib/zookeeper/log
    networks:
      - nifi_network
    deploy:
      replicas: 1
      placement:
        constraints: [node.hostname == <PHYSICAL_HOST2_HOSTNAME>]

  zookeeper3:
    image: confluentinc/cp-zookeeper:7.3.2
    hostname: zookeeper3
    ports:
      - "2183:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_SERVER_ID: 3
      ZOOKEEPER_SERVERS: zookeeper1:2888:3888;zookeeper2:2888:3888;zookeeper3:2888:3888
    volumes:
      - zookeeper3_data:/var/lib/zookeeper/data
      - zookeeper3_log:/var/lib/zookeeper/log
    networks:
      - nifi_network
    deploy:
      replicas: 1
      placement:
        constraints: [node.hostname == <PHYSICAL_HOST3_HOSTNAME>]

  nifi:
    image: apache/nifi:1.23.2 # 或更新版本
    ports:
      - "8443:8443" # NiFi UI (HTTPS)
      - "8082:8082" # NiFi Cluster Node Protocol Port
    environment:
      NIFI_WEB_HTTPS_PORT: "8443"
      NIFI_CLUSTER_IS_NODE: "true"
      NIFI_CLUSTER_NODE_PROTOCOL_PORT: "8082"
      NIFI_ZK_CONNECT_STRING: "zookeeper1:2181,zookeeper2:2181,zookeeper3:2181"
      NIFI_ZK_ROOT_NODE: "/nifi"
      NIFI_SENSITIVE_PROPS_KEY: "yourSuperSecureKey123!" # 必须设置
      NIFI_JVM_HEAP_INIT: "2g"
      NIFI_JVM_HEAP_MAX: "4g"
      # SINGLE_USER_CREDENTIALS_USERNAME: "admin" # 如果使用单用户认证
      # SINGLE_USER_CREDENTIALS_PASSWORD: "YourSecurePasswordHere"
    volumes:
      - nifi_state:/opt/nifi/nifi-current/state
      - nifi_database_repository:/opt/nifi/nifi-current/database_repository
      - nifi_flowfile_repository:/opt/nifi/nifi-current/flowfile_repository
      - nifi_content_repository:/opt/nifi/nifi-current/content_repository
      - nifi_provenance_repository:/opt/nifi/nifi-current/provenance_repository
      # - nifi_conf:/opt/nifi/nifi-current/conf # 如果需要自定义 conf
    networks:
      - nifi_network
    depends_on:
      - zookeeper1
      - zookeeper2
      - zookeeper3
    deploy:
      mode: replicated
      replicas: 3 # 部署3个 NiFi 节点实例
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure

volumes:
  zookeeper1_data:
  zookeeper1_log:
  zookeeper2_data:
  zookeeper2_log:
  zookeeper3_data:
  zookeeper3_log:
  nifi_state:
  nifi_database_repository:
  nifi_flowfile_repository:
  nifi_content_repository:
  nifi_provenance_repository:
  # nifi_conf:

networks:
  nifi_network:
    driver: overlay # Swarm 模式下使用 overlay 网络实现跨主机通信
    attachable: true