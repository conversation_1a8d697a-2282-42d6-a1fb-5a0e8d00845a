# Windows 系统 Kafka 集群使用说明

## 文件说明

在 Windows 系统上，请使用以下文件：

### ✅ Windows 可用文件
- **kafka-scripts.bat** - Windows 批处理脚本
- **kafka-scripts.ps1** - PowerShell 脚本 (推荐)
- **start-kafka.bat** - 快速启动脚本

### ❌ 不适用文件
- **kafka-scripts.sh** - Linux/Unix shell 脚本，Windows 无法直接执行

## 使用方法

### 1. 启动 Kafka 集群

```cmd
# 使用快速启动脚本
start-kafka.bat
```

或者手动启动：
```cmd
# 启动 ZooKeeper 集群
docker-compose up -d zookeeper1 zookeeper2 zookeeper3

# 等待30秒后启动 Kafka 集群
docker-compose up -d kafka1 kafka2 kafka3

# 启动管理界面
docker-compose up -d kafka-ui
```

### 2. 使用管理脚本

#### 方式1: 使用 PowerShell 脚本 (推荐)

```powershell
# 测试连接
.\kafka-scripts.ps1 test-connection

# 列出主题
.\kafka-scripts.ps1 list-topics

# 创建主题
.\kafka-scripts.ps1 create-topic test-topic 3 3

# 生产消息
.\kafka-scripts.ps1 produce test-topic

# 消费消息
.\kafka-scripts.ps1 consume test-topic my-group

# 查看帮助
.\kafka-scripts.ps1 help
```

#### 方式2: 使用批处理脚本

```cmd
# 测试连接
kafka-scripts.bat test-connection

# 列出主题
kafka-scripts.bat list-topics

# 创建主题
kafka-scripts.bat create-topic test-topic 3 3

# 生产消息
kafka-scripts.bat produce test-topic

# 消费消息
kafka-scripts.bat consume test-topic my-group

# 查看帮助
kafka-scripts.bat help
```

### 3. 访问管理界面

- **Kafka UI**: http://localhost:19090
- **ZooNavigator**: http://localhost:19000

### 4. 连接配置

如果您要在应用程序中连接 Kafka，请使用以下配置：

```properties
bootstrap.servers=localhost:9092,localhost:9093,localhost:9094
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="adminpassword";
```

## PowerShell 执行策略

如果 PowerShell 脚本无法执行，可能需要修改执行策略：

```powershell
# 查看当前执行策略
Get-ExecutionPolicy

# 临时允许执行脚本 (推荐)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 或者直接执行脚本
PowerShell -ExecutionPolicy Bypass -File .\kafka-scripts.ps1 test-connection
```

## 常见问题

### 1. 编码问题
- 确保使用 UTF-8 编码保存文件
- 在 PowerShell 中设置编码：`[Console]::OutputEncoding = [System.Text.Encoding]::UTF8`

### 2. 容器未运行
```cmd
# 检查容器状态
docker-compose ps

# 查看日志
docker-compose logs kafka1
```

### 3. 端口冲突
确保以下端口未被占用：
- 9092, 9093, 9094 (Kafka)
- 19090 (Kafka UI)
- 19000 (ZooNavigator)

## 测试步骤

1. **启动集群**：
   ```cmd
   start-kafka.bat
   ```

2. **测试连接**：
   ```powershell
   .\kafka-scripts.ps1 test-connection
   ```

3. **创建测试主题**：
   ```powershell
   .\kafka-scripts.ps1 create-topic test-topic
   ```

4. **访问管理界面**：
   - 打开浏览器访问 http://localhost:19090

5. **生产和消费消息**：
   ```powershell
   # 在一个终端中生产消息
   .\kafka-scripts.ps1 produce test-topic
   
   # 在另一个终端中消费消息
   .\kafka-scripts.ps1 consume test-topic
   ```

## 注意事项

1. **文件编码**：所有脚本文件都使用 UTF-8 编码保存
2. **路径分隔符**：Windows 使用反斜杠 `\`，Linux 使用正斜杠 `/`
3. **命令差异**：Windows 使用 `findstr` 而不是 `grep`，使用 `timeout` 而不是 `sleep`
4. **权限**：确保 Docker 有足够的权限运行容器

如果遇到任何问题，请检查：
- Docker 是否正常运行
- 端口是否被占用
- 防火墙设置
- 文件编码格式
