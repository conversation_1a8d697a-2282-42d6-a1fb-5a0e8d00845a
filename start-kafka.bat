@echo off
echo 启动 Kafka 集群...

echo 1. 停止现有容器...
docker-compose down

echo 2. 启动 ZooKeeper 集群...
docker-compose up -d zookeeper1 zookeeper2 zookeeper3

echo 3. 等待 ZooKeeper 集群启动...
timeout /t 30 /nobreak

echo 4. 启动 Kafka 集群...
docker-compose up -d kafka1 kafka2 kafka3

echo 5. 等待 Kafka 集群启动...
timeout /t 30 /nobreak

echo 6. 启动 Kafka UI...
docker-compose up -d kafka-ui

echo 7. 检查服务状态...
docker-compose ps

echo.
echo Kafka 集群启动完成！
echo.
echo 访问地址:
echo - Kafka UI: http://localhost:19090
echo - ZooNavigator: http://localhost:19000
echo.
echo 连接信息:
echo - Bootstrap Servers: localhost:9092,localhost:9093,localhost:9094
echo - 用户名: admin
echo - 密码: adminpassword
echo.
echo 查看日志: docker-compose logs kafka1
echo 测试连接: kafka-scripts.bat test-connection
