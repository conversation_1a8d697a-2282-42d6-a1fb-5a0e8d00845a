services:
  # data extraction, transformation and load service
  nifi:
    build: ./nifi_service
    restart: on-failure
    ports:
      - '${NIFI_WEB_HTTPS_PORT}:8443'
      # - '18080:8080'
    environment:
      - NIFI_WEB_HTTPS_HOST=${NIFI_WEB_HTTPS_HOST:-nifi}
      - NIFI_SENSITIVE_PROPS_KEY=${NIFI_SENSITIVE_PROPS_KEY:-0123456789aBcDeF}
      - SINGLE_USER_CREDENTIALS_USERNAME=${NIFI_SINGLE_USER_CREDENTIALS_USERNAME:-admin}
      - SINGLE_USER_CREDENTIALS_PASSWORD=${NIFI_SINGLE_USER_CREDENTIALS_PASSWORD:-dEtDVn6f4HK5xf7vapjqhtp61GIA6kj8}
      # # cluster configuration
      # - NIFI_CLUSTER_IS_NODE=true
      # - NIFI_CLUSTER_NODE_PROTOCOL_PORT=8082
      # - NIFI_ZK_CONNECT_STRING=zookeeper:2181
      # - NIFI_ELECTION_MAX_WAIT=30 sec
      # [DEV] add `127.0.0.1 nifi` to host
      - NIFI_WEB_PROXY_HOST=${NIFI_WEB_PROXY_HOST:-localhost:8443,nifi:8443}
    healthcheck:
      test: ["CMD", "curl", "-k", "https://nifi:8443/nifi-api/access/config"]
      interval: "60s"
      timeout: "3s"
      start_period: "5s"
      retries: 5
    volumes:
      - nifi_database_repo:/opt/nifi/nifi-current/database_repository
      - nifi_flowfile_repo:/opt/nifi/nifi-current/flowfile_repository
      - nifi_content_repo:/opt/nifi/nifi-current/content_repository
      - nifi_provenance_repo:/opt/nifi/nifi-current/provenance_repository
      - nifi_state:/opt/nifi/nifi-current/state
      - nifi_logs:/opt/nifi/nifi-current/logs
      # uncomment the next line after copying the /conf directory from the container to your local directory to persist NiFi flows
      #- ./data/nifi/conf:/opt/nifi/nifi-current/conf
  # version control for nifi flows
  registry:
    image: 'm.daocloud.io/docker.io/apache/nifi-registry:2.4.0'
    restart: on-failure
    ports:
      - "${NIFI_REGISTRY_WEB_HTTPS_PORT}:18443"
      - "${NIFI_REGISTRY_WEB_HTTP_PORT}:18080"
    environment:
      - LOG_LEVEL=${NIFI_REGISTRY_LOGGING_LEVEL:-INFO}
      - NIFI_REGISTRY_WEB_HTTP_PORT=18080
      - NIFI_REGISTRY_WEB_HTTP_HOST=${NIFI_REGISTRY_WEB_HTTP_HOST:-nifi-registry}
      # - AUTH=${NIFI_REGISTRY_AUTH:-tls}
      # - NIFI_REGISTRY_WEB_HTTPS_PORT=18443
      # - NIFI_REGISTRY_WEB_HTTPS_HOST=${NIFI_REGISTRY_WEB_HTTPS_HOST:-nifi-registry}
      - NIFI_REGISTRY_DB_DIR=/opt/nifi-registry/nifi-registry-current/database
      - NIFI_REGISTRY_FLOW_PROVIDER=file
      - NIFI_REGISTRY_FLOW_STORAGE_DIR=/opt/nifi-registry/nifi-registry-current/flow_storage
    user: root
    volumes:
      - nifi-registry_database_repo:/opt/nifi-registry/nifi-registry-current/database
      - nifi-registry_flow_repo:/opt/nifi-registry/nifi-registry-current/flow_storage
      - nifi-registry_logs:/opt/nifi-registry/nifi-registry-current/logs
      # uncomment the next line after copying the /conf directory from the container to your local directory
      - ./data/nifi_registry/conf:/opt/nifi-registry/nifi-registry-current/conf
  # # configuration manager for NiFi
  # zookeeper:
  #   image: 'm.daocloud.io/docker.io/bitnami/zookeeper:3.9'
  #   restart: on-failure
  #   ports:
  #     - "${ZK_ADMIN_SERVER_PORT}:8080"
  #   environment:
  #     # - ALLOW_ANONYMOUS_LOGIN=yes
  #     - ZOO_PORT_NUMBER=2181
  #     - ZOO_ENABLE_ADMIN_SERVER=${ZK_ENABLE_ADMIN_SERVER:-yes}
  #     - ZOO_ADMIN_SERVER_PORT_NUMBER=8080
  #     - ZOO_ENABLE_AUTH=${ZK_ENABLE_AUTH:-true}
  #     - ZOO_CLIENT_USER=${ZK_CLIENT_USER:-user}
  #     - ZOO_CLIENT_PASSWORD=${ZK_CLIENT_PASSWORD:-gtlX91bnff90Ca}
  #     - ZOO_SERVER_USERS=${ZK_SERVER_USERS:-admin}
  #     - ZOO_SERVER_PASSWORDS=${ZK_SERVER_PASSWORDS:-Im9mzhl6XMwy5h}

volumes:
  nifi_database_repo:
  nifi_flowfile_repo:
  nifi_content_repo:
  nifi_provenance_repo:
  nifi_state:
  nifi_logs:
  nifi-registry_database_repo:
  nifi-registry_flow_repo:
  nifi-registry_logs:
