
https://github.com/crystalloide/Nifi-Nifi-Registry-Zookeeper
https://github.com/peter279k/nifi_persistence

## Apache NiFi 验证配置

查看自动生成的用户名和密码

```bash
docker logs nifi | grep Generated
```

### 指定单用户身份验证凭据

密码必须至少包含 12 个字符，否则 NiFi 将生成随机的用户名和密码。请参阅 secure.sh 和 start.sh 脚本

```yaml
  SINGLE_USER_CREDENTIALS_USERNAME: ${NIFI_SINGLE_USER_CREDENTIALS_USERNAME:admin}
  SINGLE_USER_CREDENTIALS_PASSWORD: ${NIFI_SINGLE_USER_CREDENTIALS_PASSWORD:admin}
```

### 使用 HTTPS 和相互 TLS 身份验证保护的独立实例

```yaml
  AUTH: tls
  KEYSTORE_PATH: /opt/certs/keystore.jks
  KEYSTORE_TYPE: J<PERSON>
  KEYSTORE_PASSWORD: QKZv1hSWAFQYZ+WU1jjF5ank+l4igeOfQRp+OSbkkrs
  TRUSTSTORE_PATH: /opt/certs/truststore.jks
  TRUSTSTORE_PASSWORD: rHkWR1gDNW3R9hgbeRsT3OM3Ue0zwGtQqcFKJD2EXWE
  TRUSTSTORE_TYPE: JKS
  INITIAL_ADMIN_IDENTITY: 'CN=Random User, O=Apache, OU=NiFi, C=US'
```

### 对于使用 SIMPLE 身份验证的 LDAP 服务器的最小连接

```yaml
  AUTH: ldap 
  KEYSTORE_PATH: /opt/certs/keystore.jks 
  KEYSTORE_TYPE: JKS 
  KEYSTORE_PASSWORD: QKZv1hSWAFQYZ+WU1jjF5ank+l4igeOfQRp+OSbkkrs 
  TRUSTSTORE_PATH: /opt/certs/truststore.jks 
  TRUSTSTORE_PASSWORD: rHkWR1gDNW3R9hgbeRsT3OM3Ue0zwGtQqcFKJD2EXWE 
  TRUSTSTORE_TYPE: JKS 
  INITIAL_ADMIN_IDENTITY: 'cn=admin,dc=example,dc=org' 
  LDAP_AUTHENTICATION_STRATEGY: 'SIMPLE' 
  LDAP_MANAGER_DN: 'cn=admin,dc=example,dc=org' 
  LDAP_MANAGER_PASSWORD: 'password' 
  LDAP_USER_SEARCH_BASE: 'dc=example,dc=org' 
  LDAP_USER_SEARCH_FILTER: 'cn={0}' 
  LDAP_IDENTITY_STRATEGY: 'USE_DN' 
  LDAP_URL: 'ldap://ldap:389'
```

## 自签名证书

```bash
# 创建 NiFi CA 和证书
keytool -genkeypair -alias nifi -keyalg RSA -keysize 2048 -storetype PKCS12 -keystore nifi-keystore.p12 -storepass Kp9#mN8xR2wL5v -keypass Qz7&fH4sB9jE3n -validity 3650 -dname "CN=nifi, OU=Dev, O=BDTD, L=JN, ST=SD, C=CN"
# 验证证书
keytool -list -keystore nifi-keystore.p12 -storetype PKCS12 -storepass Kp9#mN8xR2wL5v

# 创建 Registry 证书
keytool -genkeypair -alias registry -keyalg RSA -keysize 2048 -storetype PKCS12 -keystore registry-keystore.p12 -storepass Xt6@dW9mP3kS7u -keypass Vb4$yC8nT5rG1j -validity 3650 -dname "CN=nifi-registry, OU=Dev, O=BDTD, L=JN, ST=SD, C=CN"
# 验证证书
keytool -list -keystore registry-keystore.p12 -storetype PKCS12 -storepass Xt6@dW9mP3kS7u
```

## Nginx 代理

```nginx
server {
  listen       81;
  server_name  localhost;

  location /nifi/ {
    proxy_ssl_certificate     C:/nifi-toolkit-1.7.1/target/nifi-cert.pem;
    proxy_ssl_certificate_key C:/nifi-toolkit-1.7.1/target/nifi-key.key;
    proxy_ssl_server_name on;

    proxy_pass https://localhost:8443;
    proxy_set_header X-ProxyScheme "https";
    proxy_set_header X-ProxyHost $http_host;
    proxy_set_header X-ProxyPort 8443;
    proxy_set_header X-ProxyContextPath "";
    root   html;
    index  index.html index.htm;
  }

  location /nifi-api/ {
    proxy_ssl_certificate     C:/nifi-toolkit-1.7.1/target/nifi-cert.pem;
    proxy_ssl_certificate_key C:/nifi-toolkit-1.7.1/target/nifi-key.key;
    proxy_ssl_server_name on;

    proxy_set_header X-ProxyScheme "https";
    proxy_set_header X-ProxyHost $http_host;
    proxy_set_header X-ProxyPort 443;
    proxy_set_header X-ProxyContextPath "";
    proxy_pass https://localhost:8443/nifi-api/;
  }
}
```
