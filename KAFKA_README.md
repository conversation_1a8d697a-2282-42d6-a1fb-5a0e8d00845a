# Kafka 集群配置说明

## 概述

本 docker-compose 配置包含了一个完整的 Apache Kafka 3节点集群，配置了 SASL 认证，并使用现有的 ZooKeeper 集群进行协调。

## 集群架构

### ZooKeeper 集群

- zookeeper1: ************:2181
- zookeeper2: ************:2181  
- zookeeper3: ************:2181

### Kafka 集群

- kafka1: ************:9092 (外部端口: 9092)
- kafka2: ************:9092 (外部端口: 9093)
- kafka3: ************:9092 (外部端口: 9094)

### 管理界面

- Kafka UI: http://localhost:19090 (************)
- ZooNavigator: http://localhost:19000 (************)

## 认证配置

### SASL 认证

- 协议: SASL_PLAINTEXT
- 机制: PLAIN
- 用户名: admin
- 密码: adminpassword

## 启动集群

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看 Kafka 日志
docker-compose logs kafka1
docker-compose logs kafka2
docker-compose logs kafka3
```

## 连接到 Kafka

### 使用 Kafka 客户端连接

#### 连接配置

```properties
bootstrap.servers=localhost:9092,localhost:9093,localhost:9094
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="adminpassword";
```

#### 命令行工具示例

进入 Kafka 容器：
```bash
docker exec -it kafka1 /bin/bash
```

创建主题：

```bash
kafka-topics.sh --create \
  --bootstrap-server kafka1:9092 \
  --topic test-topic \
  --partitions 3 \
  --replication-factor 3 \
  --command-config /opt/bitnami/kafka/config/client.properties
```

列出主题：

```bash
kafka-topics.sh --list \
  --bootstrap-server kafka1:9092 \
  --command-config /opt/bitnami/kafka/config/client.properties
```

生产消息：

```bash
kafka-console-producer.sh \
  --bootstrap-server kafka1:9092 \
  --topic test-topic \
  --producer.config /opt/bitnami/kafka/config/client.properties
```

消费消息：

```bash
kafka-console-consumer.sh \
  --bootstrap-server kafka1:9092 \
  --topic test-topic \
  --from-beginning \
  --consumer.config /opt/bitnami/kafka/config/client.properties
```

## 客户端配置文件

在 Kafka 容器内，客户端配置文件位于 `/opt/bitnami/kafka/config/client.properties`，内容如下：

```properties
security.protocol=SASL_PLAINTEXT
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="admin" password="adminpassword";
```

## 集群配置详情

### 复制因子配置

- 默认复制因子: 3
- 最小同步副本: 2
- 偏移量主题复制因子: 3
- 事务状态日志复制因子: 3

### 性能配置

- 网络线程数: 3
- IO 线程数: 8
- 默认分区数: 3
- 日志保留时间: 168小时 (7天)

## 监控和管理

### Kafka UI

访问 http://localhost:19090 可以使用 Kafka UI 进行集群监控和管理，包括：
- 查看集群状态
- 管理主题
- 查看消费者组
- 监控消息流量

### ZooNavigator

访问 http://localhost:19000 可以使用 ZooNavigator 查看 ZooKeeper 集群状态。

## 故障排除

### 检查服务状态

```bash
# 检查所有服务
docker-compose ps

# 检查特定服务日志
docker-compose logs kafka1
docker-compose logs kafka2
docker-compose logs kafka3
```

### 验证集群连通性

```bash
# 进入 Kafka 容器
docker exec -it kafka1 /bin/bash

# 检查集群元数据
kafka-broker-api-versions.sh --bootstrap-server kafka1:9092 --command-config /opt/bitnami/kafka/config/client.properties
```

### 常见问题

1. **认证失败**: 确保使用正确的用户名和密码 (admin/adminpassword)
2. **连接超时**: 确保所有 ZooKeeper 节点都已启动
3. **端口冲突**: 检查端口 9092, 9093, 9094, 19090 是否被占用

## 数据持久化

Kafka 数据存储在以下 Docker volumes 中：
- kafka1_data
- kafka2_data  
- kafka3_data

这些卷确保了即使容器重启，Kafka 数据也不会丢失。

## 安全注意事项

1. 当前配置使用 SASL_PLAINTEXT，在生产环境中建议使用 SASL_SSL
2. 默认密码应该在生产环境中更改
3. 考虑配置网络隔离和防火墙规则
