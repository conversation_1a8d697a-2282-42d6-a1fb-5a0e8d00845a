# Kafka 集群管理脚本 (PowerShell 版本)
# 使用方法: .\kafka-scripts.ps1 [command] [options]

param(
    [Parameter(Position=0)]
    [string]$Command = "help",
    
    [Parameter(Position=1)]
    [string]$TopicName = "",
    
    [Parameter(Position=2)]
    [int]$Partitions = 3,
    
    [Parameter(Position=3)]
    [int]$ReplicationFactor = 3,
    
    [Parameter(Position=4)]
    [string]$GroupName = ""
)

$KAFKA_CONTAINER = "kafka1"
$BOOTSTRAP_SERVERS = "kafka1:9092,kafka2:9092,kafka3:9092"
$CONFIG_FILE = "/opt/bitnami/kafka/config/client.properties"

function Show-Help {
    Write-Host "Kafka 集群管理脚本" -ForegroundColor Blue
    Write-Host ""
    Write-Host "使用方法: .\kafka-scripts.ps1 [command] [options]"
    Write-Host ""
    Write-Host "可用命令:" -ForegroundColor Green
    Write-Host "  list-topics                    - 列出所有主题"
    Write-Host "  create-topic <name> [partitions] [replication] - 创建主题"
    Write-Host "  delete-topic <name>            - 删除主题"
    Write-Host "  describe-topic <name>          - 描述主题详情"
    Write-Host "  list-consumer-groups           - 列出消费者组"
    Write-Host "  describe-consumer-group <name> - 描述消费者组"
    Write-Host "  produce <topic>                - 生产消息到主题"
    Write-Host "  consume <topic> [group]        - 消费主题消息"
    Write-Host "  cluster-info                   - 显示集群信息"
    Write-Host "  broker-list                    - 列出所有 broker"
    Write-Host "  test-connection                - 测试连接"
    Write-Host "  help                           - 显示此帮助信息"
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\kafka-scripts.ps1 create-topic test-topic 3 3"
    Write-Host "  .\kafka-scripts.ps1 produce test-topic"
    Write-Host "  .\kafka-scripts.ps1 consume test-topic my-group"
}

function Test-KafkaContainer {
    $containerRunning = docker ps --format "table {{.Names}}" | Select-String $KAFKA_CONTAINER
    if (-not $containerRunning) {
        Write-Host "错误: Kafka 容器 $KAFKA_CONTAINER 未运行" -ForegroundColor Red
        exit 1
    }
}

function Invoke-KafkaCommand {
    param([string]$KafkaCmd)
    docker exec -it $KAFKA_CONTAINER $KafkaCmd
}

function Get-Topics {
    Test-KafkaContainer
    Write-Host "列出所有主题:" -ForegroundColor Green
    Invoke-KafkaCommand "kafka-topics.sh --list --bootstrap-server $BOOTSTRAP_SERVERS --command-config $CONFIG_FILE"
}

function New-Topic {
    param([string]$Name, [int]$Parts, [int]$Repl)
    
    if ([string]::IsNullOrEmpty($Name)) {
        Write-Host "错误: 请提供主题名称" -ForegroundColor Red
        return
    }
    
    Test-KafkaContainer
    Write-Host "创建主题: $Name (分区: $Parts, 副本: $Repl)" -ForegroundColor Green
    Invoke-KafkaCommand "kafka-topics.sh --create --bootstrap-server $BOOTSTRAP_SERVERS --topic $Name --partitions $Parts --replication-factor $Repl --command-config $CONFIG_FILE"
}

function Remove-Topic {
    param([string]$Name)
    
    if ([string]::IsNullOrEmpty($Name)) {
        Write-Host "错误: 请提供主题名称" -ForegroundColor Red
        return
    }
    
    Test-KafkaContainer
    Write-Host "删除主题: $Name" -ForegroundColor Yellow
    $confirm = Read-Host "确认删除主题 '$Name'? (y/N)"
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        Invoke-KafkaCommand "kafka-topics.sh --delete --bootstrap-server $BOOTSTRAP_SERVERS --topic $Name --command-config $CONFIG_FILE"
    } else {
        Write-Host "取消删除操作"
    }
}

function Get-TopicDetails {
    param([string]$Name)
    
    if ([string]::IsNullOrEmpty($Name)) {
        Write-Host "错误: 请提供主题名称" -ForegroundColor Red
        return
    }
    
    Test-KafkaContainer
    Write-Host "主题详情: $Name" -ForegroundColor Green
    Invoke-KafkaCommand "kafka-topics.sh --describe --bootstrap-server $BOOTSTRAP_SERVERS --topic $Name --command-config $CONFIG_FILE"
}

function Get-ConsumerGroups {
    Test-KafkaContainer
    Write-Host "列出所有消费者组:" -ForegroundColor Green
    Invoke-KafkaCommand "kafka-consumer-groups.sh --list --bootstrap-server $BOOTSTRAP_SERVERS --command-config $CONFIG_FILE"
}

function Get-ConsumerGroupDetails {
    param([string]$GroupName)
    
    if ([string]::IsNullOrEmpty($GroupName)) {
        Write-Host "错误: 请提供消费者组名称" -ForegroundColor Red
        return
    }
    
    Test-KafkaContainer
    Write-Host "消费者组详情: $GroupName" -ForegroundColor Green
    Invoke-KafkaCommand "kafka-consumer-groups.sh --describe --bootstrap-server $BOOTSTRAP_SERVERS --group $GroupName --command-config $CONFIG_FILE"
}

function Start-Producer {
    param([string]$TopicName)
    
    if ([string]::IsNullOrEmpty($TopicName)) {
        Write-Host "错误: 请提供主题名称" -ForegroundColor Red
        return
    }
    
    Test-KafkaContainer
    Write-Host "开始生产消息到主题: $TopicName" -ForegroundColor Green
    Write-Host "输入消息 (按 Ctrl+C 退出):" -ForegroundColor Yellow
    Invoke-KafkaCommand "kafka-console-producer.sh --bootstrap-server $BOOTSTRAP_SERVERS --topic $TopicName --producer.config $CONFIG_FILE"
}

function Start-Consumer {
    param([string]$TopicName, [string]$GroupName)
    
    if ([string]::IsNullOrEmpty($TopicName)) {
        Write-Host "错误: 请提供主题名称" -ForegroundColor Red
        return
    }
    
    if ([string]::IsNullOrEmpty($GroupName)) {
        $timestamp = [int][double]::Parse((Get-Date -UFormat %s))
        $GroupName = "console-consumer-$timestamp"
    }
    
    Test-KafkaContainer
    Write-Host "开始消费主题: $TopicName (消费者组: $GroupName)" -ForegroundColor Green
    Write-Host "按 Ctrl+C 退出" -ForegroundColor Yellow
    Invoke-KafkaCommand "kafka-console-consumer.sh --bootstrap-server $BOOTSTRAP_SERVERS --topic $TopicName --group $GroupName --from-beginning --consumer.config $CONFIG_FILE"
}

function Get-ClusterInfo {
    Test-KafkaContainer
    Write-Host "Kafka 集群信息:" -ForegroundColor Green
    Invoke-KafkaCommand "kafka-broker-api-versions.sh --bootstrap-server $BOOTSTRAP_SERVERS --command-config $CONFIG_FILE"
}

function Get-BrokerList {
    Test-KafkaContainer
    Write-Host "Broker 列表:" -ForegroundColor Green
    Write-Host "通过主题元数据获取 broker 信息:" -ForegroundColor Blue
    Invoke-KafkaCommand "kafka-topics.sh --describe --bootstrap-server $BOOTSTRAP_SERVERS --command-config $CONFIG_FILE"
}

function Test-Connection {
    Test-KafkaContainer
    Write-Host "测试 Kafka 集群连接..." -ForegroundColor Green
    
    $result = docker exec $KAFKA_CONTAINER kafka-broker-api-versions.sh --bootstrap-server $BOOTSTRAP_SERVERS --command-config $CONFIG_FILE 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 连接成功" -ForegroundColor Green
        Write-Host ""
        Write-Host "集群基本信息:" -ForegroundColor Blue
        Write-Host "Bootstrap Servers: $BOOTSTRAP_SERVERS"
        Write-Host "认证方式: SASL_PLAINTEXT"
        Write-Host "用户名: admin"
        
        Write-Host "获取主题数量..." -ForegroundColor Blue
        $topicCount = (docker exec $KAFKA_CONTAINER kafka-topics.sh --list --bootstrap-server $BOOTSTRAP_SERVERS --command-config $CONFIG_FILE 2>$null | Measure-Object -Line).Lines
        Write-Host "主题数量: $topicCount"
    } else {
        Write-Host "✗ 连接失败" -ForegroundColor Red
        exit 1
    }
}

# 主逻辑
switch ($Command.ToLower()) {
    "list-topics" { Get-Topics }
    "create-topic" { New-Topic -Name $TopicName -Parts $Partitions -Repl $ReplicationFactor }
    "delete-topic" { Remove-Topic -Name $TopicName }
    "describe-topic" { Get-TopicDetails -Name $TopicName }
    "list-consumer-groups" { Get-ConsumerGroups }
    "describe-consumer-group" { Get-ConsumerGroupDetails -GroupName $TopicName }
    "produce" { Start-Producer -TopicName $TopicName }
    "consume" { Start-Consumer -TopicName $TopicName -GroupName $GroupName }
    "cluster-info" { Get-ClusterInfo }
    "broker-list" { Get-BrokerList }
    "test-connection" { Test-Connection }
    "help" { Show-Help }
    default { 
        Write-Host "未知命令: $Command" -ForegroundColor Red
        Show-Help 
    }
}
