<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at
      http://www.apache.org/licenses/LICENSE-2.0
  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!--
    This file lists the userGroupProviders, accessPolicyProviders, and authorizers to use when running securely. In order
    to use a specific authorizer it must be configured here and its identifier must be specified in the nifi-registry.properties file.
    If the authorizer is a managedAuthorizer, it may need to be configured with an accessPolicyProvider and an userGroupProvider.
    This file allows for configuration of them, but they must be configured in order:

    ...
    all userGroupProviders
    all accessPolicyProviders
    all Authorizers
    ...
-->
<authorizers>

    <!--
        The FileUserGroupProvider will provide support for managing users and groups which is backed by a file
        on the local file system.

        - Users File - The file where the FileUserGroupProvider will store users and groups.

        - Initial User Identity [unique key] - The identity of a users and systems to seed the Users File. The name of
            each property must be unique, for example: "Initial User Identity A", "Initial User Identity B",
            "Initial User Identity C" or "Initial User Identity 1", "Initial User Identity 2", "Initial User Identity 3"

            NOTE: Any identity mapping rules specified in nifi-registry.properties will also be applied to the user identities,
            so the values should be the unmapped identities (i.e. full DN from a certificate).
    -->
    <userGroupProvider>
        <identifier>file-user-group-provider</identifier>
        <class>org.apache.nifi.registry.security.authorization.file.FileUserGroupProvider</class>
        <property name="Users File">./conf/users.xml</property>
        <property name="Initial User Identity 1"><!--CN=abc, OU=xyz--></property>
    </userGroupProvider>

    <!--
        The DatabaseUserGroupProvider will provide support for managing users and groups in a relational database. The framework
        will provide a database connection to this provider using the same database information from nifi-registry.properties.

        - Initial User Identity [unique key] - Same as the Initial User Identity in the FileUserGroupProvider
    -->
    <!-- To enable the database-user-group-provider remove 2 lines. This is 1 of 2.
    <userGroupProvider>
        <identifier>database-user-group-provider</identifier>
        <class>org.apache.nifi.registry.security.authorization.database.DatabaseUserGroupProvider</class>
        <property name="Initial User Identity 1"></property>
    </userGroupProvider>
    To enable the database-user-group-provider remove 2 lines. This is 2 of 2. -->

    <!--
        The LdapUserGroupProvider will retrieve users and groups from an LDAP server. The users and groups
        are not configurable.

        'Authentication Strategy' - How the connection to the LDAP server is authenticated. Possible
            values are ANONYMOUS, SIMPLE, LDAPS, or START_TLS.

        'Manager DN' - The DN of the manager that is used to bind to the LDAP server to search for users.
        'Manager Password' - The password of the manager that is used to bind to the LDAP server to
            search for users.

        'TLS - Keystore' - Path to the Keystore that is used when connecting to LDAP using LDAPS or START_TLS.
        'TLS - Keystore Password' - Password for the Keystore that is used when connecting to LDAP
            using LDAPS or START_TLS.
        'TLS - Keystore Type' - Type of the Keystore that is used when connecting to LDAP using
            LDAPS or START_TLS (i.e. JKS or PKCS12).
        'TLS - Truststore' - Path to the Truststore that is used when connecting to LDAP using LDAPS or START_TLS.
        'TLS - Truststore Password' - Password for the Truststore that is used when connecting to
            LDAP using LDAPS or START_TLS.
        'TLS - Truststore Type' - Type of the Truststore that is used when connecting to LDAP using
            LDAPS or START_TLS (i.e. JKS or PKCS12).
        'TLS - Client Auth' - Client authentication policy when connecting to LDAP using LDAPS or START_TLS.
            Possible values are REQUIRED, WANT, NONE.
        'TLS - Protocol' - Protocol to use when connecting to LDAP using LDAPS or START_TLS. (i.e. TLS,
            TLSv1.1, TLSv1.2, etc).
        'TLS - Shutdown Gracefully' - Specifies whether the TLS should be shut down gracefully
            before the target context is closed. Defaults to false.

        'Referral Strategy' - Strategy for handling referrals. Possible values are FOLLOW, IGNORE, THROW.
        'Connect Timeout' - Duration of connect timeout. (i.e. 10 secs).
        'Read Timeout' - Duration of read timeout. (i.e. 10 secs).

        'Url' - Space-separated list of URLs of the LDAP servers (i.e. ldap://<hostname>:<port>).
        'Page Size' - Sets the page size when retrieving users and groups. If not specified, no paging is performed.
        'Sync Interval' - Duration of time between syncing users and groups. (i.e. 30 mins).
        'Group Membership - Enforce Case Sensitivity' - Sets whether group membership decisions are case sensitive. When a user or group
            is inferred (by not specifying or user or group search base or user identity attribute or group name attribute) case sensitivity
            is enforced since the value to use for the user identity or group name would be ambiguous. Defaults to false.

        'User Search Base' - Base DN for searching for users (i.e. ou=users,o=nifi). Required to search users.
        'User Object Class' - Object class for identifying users (i.e. person). Required if searching users.
        'User Search Scope' - Search scope for searching users (ONE_LEVEL, OBJECT, or SUBTREE). Required if searching users.
        'User Search Filter' - Filter for searching for users against the 'User Search Base' (i.e. (memberof=cn=team1,ou=groups,o=nifi) ). Optional.
        'User Identity Attribute' - Attribute to use to extract user identity (i.e. cn). Optional. If not set, the entire DN is used.
        'User Group Name Attribute' - Attribute to use to define group membership (i.e. memberof). Optional. If not set
            group membership will not be calculated through the users. Will rely on group membership being defined
            through 'Group Member Attribute' if set. The value of this property is the name of the attribute in the user ldap entry that
            associates them with a group. The value of that user attribute could be a dn or group name for instance. What value is expected
            is configured in the 'User Group Name Attribute - Referenced Group Attribute'.
        'User Group Name Attribute - Referenced Group Attribute' - If blank, the value of the attribute defined in 'User Group Name Attribute'
            is expected to be the full dn of the group. If not blank, this property will define the attribute of the group ldap entry that
            the value of the attribute defined in 'User Group Name Attribute' is referencing (i.e. name). Use of this property requires that
            'Group Search Base' is also configured.

        'Group Search Base' - Base DN for searching for groups (i.e. ou=groups,o=nifi). Required to search groups.
        'Group Object Class' - Object class for identifying groups (i.e. groupOfNames). Required if searching groups.
        'Group Search Scope' - Search scope for searching groups (ONE_LEVEL, OBJECT, or SUBTREE). Required if searching groups.
        'Group Search Filter' - Filter for searching for groups against the 'Group Search Base'. Optional.
        'Group Name Attribute' - Attribute to use to extract group name (i.e. cn). Optional. If not set, the entire DN is used.
        'Group Member Attribute' - Attribute to use to define group membership (i.e. member). Optional. If not set
            group membership will not be calculated through the groups. Will rely on group membership being defined
            through 'User Group Name Attribute' if set. The value of this property is the name of the attribute in the group ldap entry that
            associates them with a user. The value of that group attribute could be a dn or memberUid for instance. What value is expected
            is configured in the 'Group Member Attribute - Referenced User Attribute'. (i.e. member: cn=User 1,ou=users,o=nifi-registry vs. memberUid: user1)
        'Group Member Attribute - Referenced User Attribute' - If blank, the value of the attribute defined in 'Group Member Attribute'
            is expected to be the full dn of the user. If not blank, this property will define the attribute of the user ldap entry that
            the value of the attribute defined in 'Group Member Attribute' is referencing (i.e. uid). Use of this property requires that
            'User Search Base' is also configured. (i.e. member: cn=User 1,ou=users,o=nifi-registry vs. memberUid: user1)

        NOTE: Any identity mapping rules specified in nifi-registry.properties will also be applied to the user identities.
            Group names are not mapped.
    -->
    <!-- To enable the ldap-user-group-provider remove 2 lines. This is 1 of 2.
    <userGroupProvider>
        <identifier>ldap-user-group-provider</identifier>
        <class>org.apache.nifi.registry.security.ldap.tenants.LdapUserGroupProvider</class>
        <property name="Authentication Strategy">START_TLS</property>

        <property name="Manager DN"></property>
        <property name="Manager Password"></property>

        <property name="TLS - Keystore"></property>
        <property name="TLS - Keystore Password"></property>
        <property name="TLS - Keystore Type"></property>
        <property name="TLS - Truststore"></property>
        <property name="TLS - Truststore Password"></property>
        <property name="TLS - Truststore Type"></property>
        <property name="TLS - Client Auth"></property>
        <property name="TLS - Protocol"></property>
        <property name="TLS - Shutdown Gracefully"></property>

        <property name="Referral Strategy">FOLLOW</property>
        <property name="Connect Timeout">10 secs</property>
        <property name="Read Timeout">10 secs</property>

        <property name="Url"></property>
        <property name="Page Size"></property>
        <property name="Sync Interval">30 mins</property>
        <property name="Group Membership - Enforce Case Sensitivity">false</property>

        <property name="User Search Base"></property>
        <property name="User Object Class">person</property>
        <property name="User Search Scope">ONE_LEVEL</property>
        <property name="User Search Filter"></property>
        <property name="User Identity Attribute"></property>
        <property name="User Group Name Attribute"></property>
        <property name="User Group Name Attribute - Referenced Group Attribute"></property>

        <property name="Group Search Base"></property>
        <property name="Group Object Class">group</property>
        <property name="Group Search Scope">ONE_LEVEL</property>
        <property name="Group Search Filter"></property>
        <property name="Group Name Attribute"></property>
        <property name="Group Member Attribute"></property>
        <property name="Group Member Attribute - Referenced User Attribute"></property>
    </userGroupProvider>
    To enable the ldap-user-group-provider remove 2 lines. This is 2 of 2. -->

    <!--
        The CompositeUserGroupProvider will provide support for retrieving users and groups from multiple sources.

        - User Group Provider [unique key] - The identifier of user group providers to load from. The name of
            each property must be unique, for example: "User Group Provider A", "User Group Provider B",
            "User Group Provider C" or "User Group Provider 1", "User Group Provider 2", "User Group Provider 3"

            NOTE: Any identity mapping rules specified in nifi-registry.properties are not applied in this implementation. This
            behavior would need to be applied by the base implementation.
    -->
    <!-- To enable the composite-user-group-provider remove 2 lines. This is 1 of 2.
    <userGroupProvider>
        <identifier>composite-user-group-provider</identifier>
        <class>org.apache.nifi.registry.security.authorization.CompositeUserGroupProvider</class>
        <property name="User Group Provider 1"></property>
    </userGroupProvider>
    To enable the composite-user-group-provider remove 2 lines. This is 2 of 2. -->

    <!--
        The CompositeConfigurableUserGroupProvider will provide support for retrieving users and groups from multiple sources.
        Additionally, a single configurable user group provider is required. Users from the configurable user group provider
        are configurable, however users loaded from one of the User Group Provider [unique key] will not be.

        - Configurable User Group Provider - A configurable user group provider.

        - User Group Provider [unique key] - The identifier of user group providers to load from. The name of
            each property must be unique, for example: "User Group Provider A", "User Group Provider B",
            "User Group Provider C" or "User Group Provider 1", "User Group Provider 2", "User Group Provider 3"

            NOTE: Any identity mapping rules specified in nifi-registry.properties are not applied in this implementation. This
            behavior would need to be applied by the base implementation.
    -->
    <!-- To enable the composite-configurable-user-group-provider remove 2 lines. This is 1 of 2.
    <userGroupProvider>
        <identifier>composite-configurable-user-group-provider</identifier>
        <class>org.apache.nifi.registry.security.authorization.CompositeConfigurableUserGroupProvider</class>
        <property name="Configurable User Group Provider">file-user-group-provider</property>
        <property name="User Group Provider 1"></property>
    </userGroupProvider>
    To enable the composite-configurable-user-group-provider remove 2 lines. This is 2 of 2. -->

    <!--
        The FileAccessPolicyProvider will provide support for managing access policies which is backed by a file
        on the local file system.

        - User Group Provider - The identifier for a User Group Provider defined above that will be used to access
            users and groups for use in the managed access policies.

        - Authorizations File - The file where the FileAccessPolicyProvider will store policies.

        - Initial Admin Identity - The identity of an initial admin user that will be granted access to the UI and
            given the ability to create additional users, groups, and policies. The value of this property could be
            a DN when using certificates or LDAP. This property will only be used when there
            are no other policies defined.

            NOTE: Any identity mapping rules specified in nifi-registry.properties will also be applied to the initial admin identity,
            so the value should be the unmapped identity. This identity must be found in the configured User Group Provider.

        - NiFi Identity [unique key] - The identity of a NiFi node that will have access to this NiFi Registry and will be able
            to act as a proxy on behalf of a NiFi Registry end user. A property should be created for the identity of every NiFi
            node that needs to access this NiFi Registry. The name of each property must be unique, for example for three
            NiFi clients:
            "NiFi Identity A", "NiFi Identity B", "NiFi Identity C" or "NiFi Identity 1", "NiFi Identity 2", "NiFi Identity 3"

            NOTE: Any identity mapping rules specified in nifi-registry.properties will also be applied to the nifi identities,
            so the values should be the unmapped identities (i.e. full DN from a certificate). This identity must be found
            in the configured User Group Provider.

        - NiFi Group Name - The name of the group, whose members are NiFi instance/node identities,
            that will have access to this NiFi Registry and will be able to act as a proxy on behalf of a NiFi Registry end user.
            The members of this group will be granted permission to proxy user requests, as well as read any bucket to perform synchronization checks.
    -->
    <accessPolicyProvider>
        <identifier>file-access-policy-provider</identifier>
        <class>org.apache.nifi.registry.security.authorization.file.FileAccessPolicyProvider</class>
        <property name="User Group Provider">file-user-group-provider</property>
        <property name="Authorizations File">./conf/authorizations.xml</property>
        <property name="Initial Admin Identity"><!-- CN=abc, OU=xyz --></property>
        <property name="NiFi Group Name"></property>

        <!--<property name="NiFi Identity 1"></property>-->
    </accessPolicyProvider>

    <!--
        The DatabaseAccessPolicyProvider will provide support for managing access policies in a relational database. The
        framework will provide a database connection to this provider using the same database information from nifi-registry.properties.

        - User Group Provider - Same as User Group Provider in the FileAccessPolicyProvider

        - Initial Admin Identity - Same as Initial Admin Identity in the FileAccessPolicyProvider

        - NiFi Identity [unique key] - Same as NiFi Identity in the FileAccessPolicyProvider

        - NiFi Group Name - Same as NiFi Group Name in the FileAccessPolicyProvider
    -->
    <!-- To enable the database-access-policy-provider remove 2 lines. This is 1 of 2.
    <accessPolicyProvider>
        <identifier>database-access-policy-provider</identifier>
        <class>org.apache.nifi.registry.security.authorization.database.DatabaseAccessPolicyProvider</class>
        <property name="User Group Provider">database-user-group-provider</property>
        <property name="Initial Admin Identity"></property>
        <property name="NiFi Identity 1"></property>
        <property name="NiFi Group Name"></property>
    </accessPolicyProvider>
    To enable the database-access-policy-provider remove 2 lines. This is 2 of 2. -->

    <!--
        The StandardManagedAuthorizer. This authorizer implementation must be configured with the
        Access Policy Provider which it will use to access and manage users, groups, and policies.
        These users, groups, and policies will be used to make all access decisions during authorization
        requests.

        - Access Policy Provider - The identifier for an Access Policy Provider defined above.
    -->
    <authorizer>
        <identifier>managed-authorizer</identifier>
        <class>org.apache.nifi.registry.security.authorization.StandardManagedAuthorizer</class>
        <property name="Access Policy Provider">file-access-policy-provider</property>
    </authorizer>

</authorizers>
