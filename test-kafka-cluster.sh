#!/bin/bash

# Kafka 集群测试脚本
# 用于验证 Kafka 集群是否正常工作

KAFKA_CONTAINER="kafka1"
BOOTSTRAP_SERVERS="kafka1:9092,kafka2:9092,kafka3:9092"
CONFIG_FILE="/opt/bitnami/kafka/config/client.properties"
TEST_TOPIC="test-cluster-topic"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Kafka 集群测试脚本 ===${NC}"
echo ""

# 检查 Docker 容器是否运行
echo -e "${YELLOW}1. 检查 Kafka 容器状态...${NC}"
for container in kafka1 kafka2 kafka3; do
    if docker ps | grep -q $container; then
        echo -e "${GREEN}✓ $container 正在运行${NC}"
    else
        echo -e "${RED}✗ $container 未运行${NC}"
        exit 1
    fi
done

# 测试连接
echo -e "\n${YELLOW}2. 测试 Kafka 集群连接...${NC}"
if docker exec $KAFKA_CONTAINER kafka-broker-api-versions.sh \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --command-config $CONFIG_FILE >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 连接成功${NC}"
else
    echo -e "${RED}✗ 连接失败${NC}"
    exit 1
fi

# 创建测试主题
echo -e "\n${YELLOW}3. 创建测试主题...${NC}"
docker exec $KAFKA_CONTAINER kafka-topics.sh --create \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --topic $TEST_TOPIC \
    --partitions 3 \
    --replication-factor 3 \
    --command-config $CONFIG_FILE >/dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 测试主题 '$TEST_TOPIC' 创建成功${NC}"
else
    echo -e "${YELLOW}! 测试主题可能已存在，继续测试...${NC}"
fi

# 验证主题创建
echo -e "\n${YELLOW}4. 验证主题详情...${NC}"
topic_info=$(docker exec $KAFKA_CONTAINER kafka-topics.sh --describe \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --topic $TEST_TOPIC \
    --command-config $CONFIG_FILE 2>/dev/null)

if echo "$topic_info" | grep -q "PartitionCount: 3"; then
    echo -e "${GREEN}✓ 主题分区数正确 (3)${NC}"
else
    echo -e "${RED}✗ 主题分区数不正确${NC}"
fi

if echo "$topic_info" | grep -q "ReplicationFactor: 3"; then
    echo -e "${GREEN}✓ 主题复制因子正确 (3)${NC}"
else
    echo -e "${RED}✗ 主题复制因子不正确${NC}"
fi

# 测试消息生产和消费
echo -e "\n${YELLOW}5. 测试消息生产和消费...${NC}"

# 生产测试消息
test_message="Test message $(date '+%Y-%m-%d %H:%M:%S')"
echo "$test_message" | docker exec -i $KAFKA_CONTAINER kafka-console-producer.sh \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --topic $TEST_TOPIC \
    --producer.config $CONFIG_FILE >/dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 消息生产成功${NC}"
else
    echo -e "${RED}✗ 消息生产失败${NC}"
    exit 1
fi

# 消费测试消息
echo -e "${YELLOW}  等待消息消费...${NC}"
consumed_message=$(timeout 10 docker exec $KAFKA_CONTAINER kafka-console-consumer.sh \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --topic $TEST_TOPIC \
    --from-beginning \
    --max-messages 1 \
    --timeout-ms 5000 \
    --consumer.config $CONFIG_FILE 2>/dev/null | tail -1)

if [[ "$consumed_message" == *"Test message"* ]]; then
    echo -e "${GREEN}✓ 消息消费成功${NC}"
    echo -e "${BLUE}  消费的消息: $consumed_message${NC}"
else
    echo -e "${RED}✗ 消息消费失败或超时${NC}"
fi

# 检查消费者组
echo -e "\n${YELLOW}6. 检查消费者组...${NC}"
consumer_groups=$(docker exec $KAFKA_CONTAINER kafka-consumer-groups.sh --list \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --command-config $CONFIG_FILE 2>/dev/null)

if [ -n "$consumer_groups" ]; then
    echo -e "${GREEN}✓ 消费者组列表获取成功${NC}"
    echo -e "${BLUE}  活跃的消费者组数量: $(echo "$consumer_groups" | wc -l)${NC}"
else
    echo -e "${YELLOW}! 暂无活跃的消费者组${NC}"
fi

# 检查集群元数据
echo -e "\n${YELLOW}7. 检查集群元数据...${NC}"
broker_info=$(docker exec $KAFKA_CONTAINER kafka-broker-api-versions.sh \
    --bootstrap-server $BOOTSTRAP_SERVERS \
    --command-config $CONFIG_FILE 2>/dev/null | head -5)

if [ -n "$broker_info" ]; then
    echo -e "${GREEN}✓ 集群元数据获取成功${NC}"
    echo -e "${BLUE}  Broker 信息:${NC}"
    echo "$broker_info" | while read line; do
        echo -e "${BLUE}    $line${NC}"
    done
else
    echo -e "${RED}✗ 集群元数据获取失败${NC}"
fi

# 性能测试 (可选)
echo -e "\n${YELLOW}8. 简单性能测试...${NC}"
echo -e "${BLUE}  生产 1000 条消息...${NC}"

perf_result=$(docker exec $KAFKA_CONTAINER kafka-producer-perf-test.sh \
    --topic $TEST_TOPIC \
    --num-records 1000 \
    --record-size 100 \
    --throughput 100 \
    --producer.config $CONFIG_FILE \
    --producer-props bootstrap.servers=$BOOTSTRAP_SERVERS 2>/dev/null | tail -1)

if [ -n "$perf_result" ]; then
    echo -e "${GREEN}✓ 性能测试完成${NC}"
    echo -e "${BLUE}  结果: $perf_result${NC}"
else
    echo -e "${YELLOW}! 性能测试跳过或失败${NC}"
fi

# 清理测试主题 (可选)
echo -e "\n${YELLOW}9. 清理测试数据...${NC}"
read -p "是否删除测试主题 '$TEST_TOPIC'? (y/N): " cleanup
if [[ $cleanup == [yY] || $cleanup == [yY][eE][sS] ]]; then
    docker exec $KAFKA_CONTAINER kafka-topics.sh --delete \
        --bootstrap-server $BOOTSTRAP_SERVERS \
        --topic $TEST_TOPIC \
        --command-config $CONFIG_FILE >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ 测试主题已删除${NC}"
    else
        echo -e "${RED}✗ 测试主题删除失败${NC}"
    fi
else
    echo -e "${BLUE}  保留测试主题 '$TEST_TOPIC'${NC}"
fi

echo -e "\n${GREEN}=== 测试完成 ===${NC}"
echo -e "${BLUE}集群状态总结:${NC}"
echo -e "${GREEN}✓ Kafka 3节点集群运行正常${NC}"
echo -e "${GREEN}✓ SASL 认证配置正确${NC}"
echo -e "${GREEN}✓ 消息生产和消费功能正常${NC}"
echo -e "${GREEN}✓ 集群复制和分区功能正常${NC}"

echo -e "\n${BLUE}访问管理界面:${NC}"
echo -e "${BLUE}  Kafka UI: http://localhost:19090${NC}"
echo -e "${BLUE}  ZooNavigator: http://localhost:19000${NC}"
