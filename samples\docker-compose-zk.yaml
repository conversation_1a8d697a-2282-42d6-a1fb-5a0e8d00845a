# 适用于单机部署一个 ZK 和一个 NiFi 节点, 集群需要分别调整或运行类似的配置
version: '3.8'

services:
  nifi:
    image: apache/nifi:2.4.0
    hostname: nifi${NIFI_ID}
    container_name: nifi${NIFI_ID}
    ports:
      # - "${NIFI_WEB_HTTP_PORT:8080}:8080" # HTTP 端口, 默认为空
      - "${NIFI_WEB_HTTPS_PORT:8443}:8443" # HTTPS 端口
      - "${NIFI_REMOTE_INPUT_SOCKET_PORT:10000}:10000" # 远程输入 Socket 端口
      - "${NIFI_JVM_DEBUG_PORT:8000}:8000" # JVM 调试端口
    environment:
      SINGLE_USER_CREDENTIALS_USERNAME: ${NIFI_SINGLE_USER_CREDENTIALS_USERNAME:}
      SINGLE_USER_CREDENTIALS_PASSWORD: ${NIFI_SINGLE_USER_CREDENTIALS_PASSWORD:}
      NIFI_SENSITIVE_PROPS_KEY: ${NIFI_SENSITIVE_PROPS_KEY}
      NIFI_WEB_HTTPS_PORT: ${NIFI_WEB_HTTPS_PORT:8443}
      NIFI_CLUSTER_IS_NODE: ${NIFI_CLUSTER_IS_NODE:false}
      NIFI_CLUSTER_ADDRESS: ${NIFI_CLUSTER_ADDRESS:$HOSTNAME}
      NIFI_CLUSTER_NODE_PROTOCOL_PORT: ${NIFI_CLUSTER_NODE_PROTOCOL_PORT:}
      NIFI_CLUSTER_NODE_PROTOCOL_MAX_THREADS: ${NIFI_CLUSTER_NODE_PROTOCOL_MAX_THREADS:50}
      NIFI_ZK_CONNECT_STRING: ${NIFI_ZK_CONNECT_STRING:}
      NIFI_ZK_ROOT_NODE: ${NIFI_ZK_ROOT_NODE:/nifi}
      NIFI_ELECTION_MAX_WAIT: ${NIFI_ELECTION_MAX_WAIT:5 mins}
      NIFI_ELECTION_MAX_CANDIDATES: ${NIFI_ELECTION_MAX_CANDIDATES:}
      NIFI_JVM_HEAP_INIT: ${NIFI_JVM_HEAP_INIT:2g}
      NIFI_JVM_HEAP_MAX: ${NIFI_JVM_HEAP_MAX:4g}
      NIFI_JVM_DEBUGGER: ${NIFI_JVM_DEBUGGER:} # 设置任意值启用 JVM 调试器
    volumes:
      # ====================================
      # ENV NIFI_BASE_DIR=/opt/nifi
      # ENV NIFI_HOME ${NIFI_BASE_DIR}/nifi-current
      # ENV NIFI_TOOLKIT_HOME ${NIFI_BASE_DIR}/nifi-toolkit-current
      # ENV NIFI_PID_DIR=${NIFI_HOME}/run
      # ENV NIFI_LOG_DIR=${NIFI_HOME}/logs
      # ------------------------------------
      # ${NIFI_LOG_DIR}
      # ${NIFI_HOME}/conf
      # ${NIFI_HOME}/database_repository
      # ${NIFI_HOME}/flowfile_repository
      # ${NIFI_HOME}/content_repository
      # ${NIFI_HOME}/provenance_repository
      # ${NIFI_HOME}/python_extensions
      # ${NIFI_HOME}/nar_extensions
      # ${NIFI_HOME}/state
      # ====================================
      - nifi_home:/opt/nifi/nifi-current
    networks:
      # 确保网络在不同主机上能互通 (例如 host network 或 overlay)
      - middle 
    depends_on:
      - zookeeper 

  # 每台机器上运行一个 ZK 实例，或者有专门的 ZK 集群
  zookeeper: 
    image: bitnami/zookeeper:3.9
    hostname: zookeeper${ZK_ID}
    container_name: zookeeper${ZK_ID}
    ports:
      - "${ZK_PORT_CLIENT:2181}:2181"
      - "${ZK_PORT_PEER:2888}:2888"
      - "${ZK_PORT_LEADER}:3888"
    environment:
      # 例如 1, 2, 3
      ZOO_SERVER_ID: ${ZK_ID}
      # 必须包含所有三个 ZK 节点的主机名/IP和端口
      ZOO_SERVERS: ${ZK_SERVERS}
      ZOO_PORT_NUMBER: ${ZK_PORT_CLIENT:2181}
      ZOO_ENABLE_ADMIN_SERVER: ${ZK_ENABLE_ADMIN_SERVER:yes}
      ZOO_ADMIN_SERVER_PORT_NUMBER: ${ZK_ADMIN_SERVER_PORT_NUMBER:8080}
      ZOO_ENABLE_AUTH: ${ZK_ENABLE_AUTH:true}
      ZOO_CLIENT_USER: ${ZK_CLIENT_USER:user}
      ZOO_SERVER_USERS: ${ZK_SERVER_USERS:user1,user2,admin}
      ZOO_CLIENT_PASSWORD: ${ZK_CLIENT_PASSWORD:pass4user}
      ZOO_SERVER_PASSWORDS: ${ZK_SERVER_PASSWORDS:pass4user1,pass4user2,pass4admin}
      ZOO_LOG_LEVEL: ${ZK_LOG_LEVEL:INFO}
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_log:/var/lib/zookeeper/log
    networks:
      # 确保网络在不同主机上能互通 (例如 host network 或 overlay)
      - middle

volumes:
  nifi_home:
  zookeeper_data:
  zookeeper_log:

networks:
  middle:
    # 单机 docker-compose，采用 bridge 网络
    # 跨主机，需要手动配置 overlay 网络或使用 host 网络模式 (不推荐，端口冲突风险高)
    # driver: bridge (默认)