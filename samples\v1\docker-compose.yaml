# 适用于单机部署一个 ZK 和一个 NiFi 节点, 集群需要分别调整或运行类似的配置
# version: '3.8'
services:
  nifi:
    image: apache/nifi:2.4.0
    hostname: nifi${NIFI_ID}
    container_name: nifi${NIFI_ID}
    ports:
      # - "${NIFI_WEB_HTTP_PORT}:8080" # HTTP 端口, 默认为空
      - "${NIFI_WEB_HTTPS_PORT}:8443" # HTTPS 端口
      - "${NIFI_REMOTE_INPUT_SOCKET_PORT}:10000" # 远程输入 Socket 端口
      # - "${NIFI_JVM_DEBUG_PORT}:8000" # JVM 调试端口
    environment:
      - NIFI_WEB_HTTPS_PORT=${NIFI_WEB_HTTPS_PORT}
      - NIFI_SENSITIVE_PROPS_KEY=${NIFI_SENSITIVE_PROPS_KEY}
      # ------------------------------------
      - SINGLE_USER_CREDENTIALS_USERNAME=${NIFI_SINGLE_USER_CREDENTIALS_USERNAME}
      - SINGLE_USER_CREDENTIALS_PASSWORD=${NIFI_SINGLE_USER_CREDENTIALS_PASSWORD}
      # ------------------------------------
      # - AUTH=tls
      # - NIFI_SECURITY_USER_AUTHENTICATION=certificate
      # - KEYSTORE_PATH=/opt/nifi/certs/nifi-keystore.p12
      # - KEYSTORE_TYPE=PKCS12
      # - KEY_PASSWORD=Kp9#mN8xR2wL5v
      # - TRUSTSTORE_PATH=/opt/nifi/certs/nifi-keystore.p12
      # - TRUSTSTORE_TYPE=PKCS12
      # - TRUSTSTORE_PASSWORD=Kp9#mN8xR2wL5v
      # - NIFI_JVM_HEAP_INIT=${NIFI_JVM_HEAP_INIT:-2g}
      # - NIFI_JVM_HEAP_MAX=${NIFI_JVM_HEAP_MAX:-4g}
      # - NIFI_JVM_DEBUGGER=${NIFI_JVM_DEBUGGER:-} # 设置任意值启用 JVM 调试器
      # ------------------------------------
      - NIFI_CLUSTER_IS_NODE=${NIFI_CLUSTER_IS_NODE:-false}
      - NIFI_CLUSTER_ADDRESS=${NIFI_CLUSTER_ADDRESS:-}
      - NIFI_CLUSTER_NODE_PROTOCOL_PORT=${NIFI_CLUSTER_NODE_PROTOCOL_PORT:-}
      - NIFI_CLUSTER_NODE_PROTOCOL_MAX_THREADS=${NIFI_CLUSTER_NODE_PROTOCOL_MAX_THREADS:-50}
      - NIFI_ZK_CONNECT_STRING=${NIFI_ZK_CONNECT_STRING:-}
      - NIFI_ZK_ROOT_NODE=${NIFI_ZK_ROOT_NODE:-/nifi}
      - NIFI_ELECTION_MAX_WAIT=${NIFI_ELECTION_MAX_WAIT:-5 mins}
      - NIFI_ELECTION_MAX_CANDIDATES=${NIFI_ELECTION_MAX_CANDIDATES:-}
    volumes:
      # ====================================
      # ENV NIFI_BASE_DIR=/opt/nifi
      # ENV NIFI_HOME ${NIFI_BASE_DIR}/nifi-current
      # ENV NIFI_TOOLKIT_HOME ${NIFI_BASE_DIR}/nifi-toolkit-current
      # ENV NIFI_PID_DIR=${NIFI_HOME}/run
      # ENV NIFI_LOG_DIR=${NIFI_HOME}/logs
      # ------------------------------------
      # ${NIFI_LOG_DIR}
      # ${NIFI_HOME}/conf
      # ${NIFI_HOME}/database_repository
      # ${NIFI_HOME}/flowfile_repository
      # ${NIFI_HOME}/content_repository
      # ${NIFI_HOME}/provenance_repository
      # ${NIFI_HOME}/python_extensions
      # ${NIFI_HOME}/nar_extensions
      # ${NIFI_HOME}/state
      # ====================================
      - nifi_home:/opt/nifi/nifi-current
      # - ./nifi/certs:/opt/nifi/certs
    networks:
      # 确保网络在不同主机上能互通 (例如 host network 或 overlay)
      - middle 
  nifi-registry:
    image: apache/nifi-registry:2.4.0
    container_name: nifi-registry
    ports:
      - "${NIFI_REGISTRY_WEB_HTTPS_PORT}:18443"
      # - "${NIFI_REGISTRY_WEB_HTTP_PORT}:18080"
    environment:
      - NIFI_REGISTRY_WEB_HTTPS_PORT=${NIFI_REGISTRY_WEB_HTTPS_PORT}
      # - NIFI_REGISTRY_WEB_HTTP_PORT=${NIFI_REGISTRY_WEB_HTTP_PORT}
      - NIFI_REGISTRY_SECURITY_KEYSTORE=/opt/registry/certs/registry-keystore.p12
      - NIFI_REGISTRY_SECURITY_KEYSTORE_TYPE=PKCS12
      - NIFI_REGISTRY_SECURITY_KEYSTORE_PASSWORD=Xt6@dW9mP3kS7u
      - NIFI_REGISTRY_SECURITY_TRUSTSTORE=/opt/registry/certs/registry-keystore.p12
      - NIFI_REGISTRY_SECURITY_TRUSTSTORE_TYPE=PKCS12
      - NIFI_REGISTRY_SECURITY_TRUSTSTORE_PASSWORD=Xt6@dW9mP3kS7u
    volumes:
      - nifi_registry_home:/opt/nifi-registry/nifi-registry-current
      - ./nifi-registry/certs:/opt/registry/certs
    networks:
      # 确保网络在不同主机上能互通 (例如 host network 或 overlay)
      - middle

volumes:
  nifi_home:
  nifi_registry_home:

networks:
  middle:
    # 单机 docker-compose，采用 bridge 网络
    # 跨主机，需要手动配置 overlay 网络或使用 host 网络模式 (不推荐，端口冲突风险高)
    # driver=bridge (默认)